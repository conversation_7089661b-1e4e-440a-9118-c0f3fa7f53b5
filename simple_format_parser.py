#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的格式保持PDF解析器
分别使用PyMuPDF和Docling解析PDF，生成保持原始格式的Markdown文件
"""

import os
import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from parsestudio.parse import PDFParser
from docling.datamodel.pipeline_options import (
    PdfPipelineOptions, 
    TableStructureOptions, 
    TableFormerMode
)

def clean_text_for_markdown(text):
    """清理文本用于Markdown输出"""
    if not text:
        return ""
    
    # 简单的文本清理
    lines = text.split('\n')
    cleaned_lines = []
    
    for line in lines:
        line = line.strip()
        if line:
            # 检测可能的标题
            if len(line) < 50 and any(keyword in line for keyword in ['检测报告', '声明', '基本信息', '检测项目', '接闪器', '引下线', '接地装置']):
                cleaned_lines.append(f"## {line}")
            else:
                cleaned_lines.append(line)
        else:
            cleaned_lines.append("")
    
    return '\n'.join(cleaned_lines)

def create_markdown_content(result, pdf_name, parser_name):
    """创建Markdown内容"""
    content = []
    
    # 标题
    content.extend([
        f"# {pdf_name} - {parser_name.upper()}解析结果",
        "",
        f"**解析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        "",
        "---",
        ""
    ])
    
    # 统计信息
    text_length = len(result.text.text) if result.text and result.text.text else 0
    table_count = len(result.tables) if result.tables else 0
    image_count = len(result.images) if result.images else 0
    
    content.extend([
        "## 文档统计",
        "",
        f"- 文本长度: {text_length:,} 字符",
        f"- 表格数量: {table_count} 个",
        f"- 图像数量: {image_count} 个",
        "",
        "---",
        ""
    ])
    
    # 主要内容
    if result.text and result.text.text:
        content.extend([
            "## 文档内容",
            "",
            clean_text_for_markdown(result.text.text),
            "",
            "---",
            ""
        ])
    
    # 表格
    if result.tables:
        content.extend([
            "## 表格数据",
            ""
        ])
        
        for i, table in enumerate(result.tables, 1):
            content.extend([
                f"### 表格 {i}",
                ""
            ])
            
            if table.metadata and table.metadata.page_number:
                content.append(f"*第{table.metadata.page_number}页*")
                content.append("")
            
            if table.markdown:
                content.extend([
                    table.markdown,
                    "",
                    "---",
                    ""
                ])
    
    return '\n'.join(content)

def parse_with_pymupdf(pdf_path):
    """使用PyMuPDF解析"""
    print("使用 PyMuPDF 解析PDF...")
    
    try:
        parser = PDFParser(parser="pymupdf")
        outputs = parser.run(pdf_path, modalities=["text", "tables", "images"])
        
        if outputs:
            result = outputs[0]
            pdf_name = os.path.splitext(os.path.basename(pdf_path))[0]
            
            # 创建输出目录
            output_dir = "outputs/pymupdf"
            os.makedirs(output_dir, exist_ok=True)
            
            # 保存图像
            if result.images:
                images_dir = os.path.join(output_dir, "images")
                os.makedirs(images_dir, exist_ok=True)
                
                for i, image_element in enumerate(result.images, 1):
                    page_num = image_element.metadata.page_number if image_element.metadata else i
                    image_file = os.path.join(images_dir, f"image_{i}_page_{page_num}.png")
                    image_element.image.save(image_file)
            
            # 生成Markdown
            markdown_content = create_markdown_content(result, pdf_name, "pymupdf")
            
            # 保存Markdown文件
            markdown_file = os.path.join(output_dir, f"{pdf_name}_pymupdf.md")
            with open(markdown_file, "w", encoding="utf-8") as f:
                f.write(markdown_content)
            
            print(f"PyMuPDF 解析完成:")
            print(f"  文本: {len(result.text.text) if result.text else 0:,} 字符")
            print(f"  表格: {len(result.tables) if result.tables else 0} 个")
            print(f"  图像: {len(result.images) if result.images else 0} 个")
            print(f"  文件: {markdown_file}")
            
            return markdown_file, result
            
    except Exception as e:
        print(f"PyMuPDF 解析失败: {e}")
        return None, None

def parse_with_docling(pdf_path):
    """使用Docling解析"""
    print("\n使用 Docling 解析PDF...")
    
    try:
        # 配置选项
        options = PdfPipelineOptions(
            do_ocr=False,
            do_table_structure=True,
            table_structure_options=TableStructureOptions(mode=TableFormerMode.FAST),
            generate_picture_images=True
        )
        
        parser = PDFParser(parser="docling", parser_kwargs={"pipeline_options": options})
        outputs = parser.run(pdf_path, modalities=["text", "tables", "images"])
        
        if outputs:
            result = outputs[0]
            pdf_name = os.path.splitext(os.path.basename(pdf_path))[0]
            
            # 创建输出目录
            output_dir = "outputs/docling"
            os.makedirs(output_dir, exist_ok=True)
            
            # 保存图像
            if result.images:
                images_dir = os.path.join(output_dir, "images")
                os.makedirs(images_dir, exist_ok=True)
                
                for i, image_element in enumerate(result.images, 1):
                    page_num = image_element.metadata.page_number if image_element.metadata else i
                    image_file = os.path.join(images_dir, f"image_{i}_page_{page_num}.png")
                    image_element.image.save(image_file)
            
            # 生成Markdown
            markdown_content = create_markdown_content(result, pdf_name, "docling")
            
            # 保存Markdown文件
            markdown_file = os.path.join(output_dir, f"{pdf_name}_docling.md")
            with open(markdown_file, "w", encoding="utf-8") as f:
                f.write(markdown_content)
            
            print(f"Docling 解析完成:")
            print(f"  文本: {len(result.text.text) if result.text else 0:,} 字符")
            print(f"  表格: {len(result.tables) if result.tables else 0} 个")
            print(f"  图像: {len(result.images) if result.images else 0} 个")
            print(f"  文件: {markdown_file}")
            
            return markdown_file, result
            
    except Exception as e:
        print(f"Docling 解析失败: {e}")
        return None, None

def create_comparison(pymupdf_result, docling_result, pdf_name):
    """创建对比报告"""
    print("\n生成对比报告...")
    
    comparison_dir = "outputs/comparison"
    os.makedirs(comparison_dir, exist_ok=True)
    
    # 统计数据
    pymupdf_stats = {
        'text': len(pymupdf_result.text.text) if pymupdf_result and pymupdf_result.text else 0,
        'tables': len(pymupdf_result.tables) if pymupdf_result and pymupdf_result.tables else 0,
        'images': len(pymupdf_result.images) if pymupdf_result and pymupdf_result.images else 0
    }
    
    docling_stats = {
        'text': len(docling_result.text.text) if docling_result and docling_result.text else 0,
        'tables': len(docling_result.tables) if docling_result and docling_result.tables else 0,
        'images': len(docling_result.images) if docling_result and docling_result.images else 0
    }
    
    # 生成报告
    report = [
        f"# PDF解析对比报告 - {pdf_name}",
        "",
        f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        "",
        "## 解析结果对比",
        "",
        "| 项目 | PyMuPDF | Docling | 差异 |",
        "|------|---------|---------|------|",
        f"| 文本长度 | {pymupdf_stats['text']:,} | {docling_stats['text']:,} | {docling_stats['text'] - pymupdf_stats['text']:+,} |",
        f"| 表格数量 | {pymupdf_stats['tables']} | {docling_stats['tables']} | {docling_stats['tables'] - pymupdf_stats['tables']:+} |",
        f"| 图像数量 | {pymupdf_stats['images']} | {docling_stats['images']} | {docling_stats['images'] - pymupdf_stats['images']:+} |",
        "",
        "## 特点对比",
        "",
        "### PyMuPDF",
        "- 速度快",
        "- 基础格式保持",
        "- 适合快速处理",
        "",
        "### Docling",
        "- 高质量解析",
        "- 更好的表格识别",
        "- 更完整的内容提取",
        "",
        "## 文件位置",
        "",
        f"- PyMuPDF: outputs/pymupdf/{pdf_name}_pymupdf.md",
        f"- Docling: outputs/docling/{pdf_name}_docling.md"
    ]
    
    report_file = os.path.join(comparison_dir, f"{pdf_name}_comparison.md")
    with open(report_file, "w", encoding="utf-8") as f:
        f.write('\n'.join(report))
    
    print(f"对比报告: {report_file}")
    return report_file

def main():
    """主函数"""
    print("PDF格式保持解析工具")
    print("=" * 50)
    
    # 检查PDF文件
    pdf_file = "A.pdf"
    if not os.path.exists(pdf_file):
        print(f"错误: 找不到 {pdf_file}")
        return
    
    # 解析
    pymupdf_file, pymupdf_result = parse_with_pymupdf(pdf_file)
    docling_file, docling_result = parse_with_docling(pdf_file)
    
    # 对比
    if pymupdf_result or docling_result:
        pdf_name = os.path.splitext(os.path.basename(pdf_file))[0]
        comparison_file = create_comparison(pymupdf_result, docling_result, pdf_name)
    
    print(f"\n解析完成!")
    print(f"结果保存在 outputs/ 目录中")

if __name__ == "__main__":
    main()
