#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF转Markdown使用示例
"""

from pdf_to_markdown import create_advanced_pdf_to_markdown

def convert_my_pdf():
    """转换我的PDF文件"""
    
    # 创建转换器
    converter = create_advanced_pdf_to_markdown()
    
    # 转换PDF
    result = converter(
        pdf_path="your_document.pdf",  # 替换为你的PDF文件路径
        output_path="output.md",       # 输出文件名
        parser_type="pymupdf",         # 解析器类型
        include_images=True            # 是否包含图像
    )
    
    if result:
        print(f"转换成功: {result}")
    else:
        print("转换失败")

if __name__ == "__main__":
    convert_my_pdf()
