#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Docling 使用指南和演示
详细介绍如何使用Docling解析器的各种功能
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from parsestudio.parse import PDFParser

# 导入Docling相关配置
from docling.datamodel.pipeline_options import (
    PdfPipelineOptions, 
    TableStructureOptions, 
    TableFormerMode, 
    EasyOcrOptions,
    TesseractOcrOptions
)

def basic_docling_usage():
    """基本的Docling使用方法"""
    
    print("🚀 基本Docling使用方法")
    print("=" * 50)
    
    # 最简单的使用方式
    parser = PDFParser(parser="docling")
    
    try:
        outputs = parser.run("A.pdf", modalities=["text", "tables", "images"])
        
        if outputs:
            result = outputs[0]
            print(f"✅ 解析成功!")
            print(f"   文本长度: {len(result.text.text) if result.text else 0:,} 字符")
            print(f"   表格数量: {len(result.tables) if result.tables else 0}")
            print(f"   图像数量: {len(result.images) if result.images else 0}")
            
            # 保存结果
            with open("docling_basic_output.md", "w", encoding="utf-8") as f:
                f.write(f"# Docling基本解析结果\n\n")
                f.write(result.text.text if result.text else "")
            
            print(f"📄 结果已保存到: docling_basic_output.md")
        else:
            print("❌ 解析失败")
            
    except Exception as e:
        print(f"❌ 错误: {str(e)}")

def advanced_docling_configuration():
    """高级Docling配置选项"""
    
    print("\n🔧 高级Docling配置")
    print("=" * 50)
    
    # 配置1: 高精度模式
    print("\n📊 配置1: 高精度表格识别模式")
    
    high_accuracy_options = PdfPipelineOptions(
        do_ocr=True,                    # 启用OCR
        do_table_structure=True,        # 启用表格结构识别
        table_structure_options=TableStructureOptions(
            do_cell_matching=True,      # 启用单元格匹配
            mode=TableFormerMode.ACCURATE  # 高精度模式
        ),
        ocr_options=EasyOcrOptions(
            force_full_page_ocr=True,   # 强制全页OCR
            use_gpu=False,              # 不使用GPU（兼容性更好）
            lang=['en', 'ch_sim']       # 支持英文和简体中文
        ),
        images_scale=2.0,               # 图像放大2倍提高质量
        generate_picture_images=True    # 生成图像
    )
    
    try:
        parser = PDFParser(
            parser="docling", 
            parser_kwargs={"pipeline_options": high_accuracy_options}
        )
        
        outputs = parser.run("A.pdf", modalities=["text", "tables"])
        
        if outputs:
            result = outputs[0]
            print(f"✅ 高精度模式解析成功!")
            print(f"   表格数量: {len(result.tables) if result.tables else 0}")
            
            # 保存表格到CSV
            if result.tables:
                for i, table in enumerate(result.tables):
                    if table.dataframe is not None:
                        csv_file = f"docling_table_{i+1}_accurate.csv"
                        table.dataframe.to_csv(csv_file, index=False, encoding="utf-8")
                        print(f"   表格 {i+1} 已保存: {csv_file}")
        
    except Exception as e:
        print(f"❌ 高精度模式错误: {str(e)}")
    
    # 配置2: 快速模式
    print("\n⚡ 配置2: 快速处理模式")
    
    fast_options = PdfPipelineOptions(
        do_ocr=False,                   # 禁用OCR加快速度
        do_table_structure=True,        # 保持表格识别
        table_structure_options=TableStructureOptions(
            do_cell_matching=False,     # 禁用单元格匹配
            mode=TableFormerMode.FAST   # 快速模式
        ),
        images_scale=1.0,               # 标准图像尺寸
        generate_picture_images=False   # 不生成图像
    )
    
    try:
        parser = PDFParser(
            parser="docling", 
            parser_kwargs={"pipeline_options": fast_options}
        )
        
        outputs = parser.run("A.pdf", modalities=["text", "tables"])
        
        if outputs:
            result = outputs[0]
            print(f"✅ 快速模式解析成功!")
            print(f"   文本长度: {len(result.text.text) if result.text else 0:,} 字符")
            print(f"   表格数量: {len(result.tables) if result.tables else 0}")
            
            # 保存文本
            with open("docling_fast_text.txt", "w", encoding="utf-8") as f:
                f.write(result.text.text if result.text else "")
            print(f"   文本已保存: docling_fast_text.txt")
        
    except Exception as e:
        print(f"❌ 快速模式错误: {str(e)}")

def docling_ocr_options():
    """Docling OCR选项演示"""
    
    print("\n👁️ Docling OCR选项")
    print("=" * 50)
    
    # OCR选项1: EasyOCR (推荐)
    print("\n🔍 OCR选项1: EasyOCR")
    
    easyocr_options = PdfPipelineOptions(
        do_ocr=True,
        ocr_options=EasyOcrOptions(
            force_full_page_ocr=True,
            use_gpu=False,
            lang=['en', 'ch_sim', 'ch_tra'],  # 英文、简体中文、繁体中文
        ),
        do_table_structure=True,
        images_scale=1.5,
        generate_picture_images=True
    )
    
    try:
        parser = PDFParser(
            parser="docling", 
            parser_kwargs={"pipeline_options": easyocr_options}
        )
        
        print("✅ EasyOCR配置成功")
        
    except Exception as e:
        print(f"❌ EasyOCR配置错误: {str(e)}")
    
    # OCR选项2: Tesseract
    print("\n📝 OCR选项2: Tesseract")
    
    tesseract_options = PdfPipelineOptions(
        do_ocr=True,
        ocr_options=TesseractOcrOptions(
            force_full_page_ocr=True,
            lang="eng+chi_sim",  # 英文+简体中文
        ),
        do_table_structure=True,
        images_scale=1.0,
        generate_picture_images=True
    )
    
    try:
        parser = PDFParser(
            parser="docling", 
            parser_kwargs={"pipeline_options": tesseract_options}
        )
        
        print("✅ Tesseract配置成功")
        
    except Exception as e:
        print(f"❌ Tesseract配置错误: {str(e)}")

def docling_markdown_options():
    """Docling Markdown输出选项"""
    
    print("\n📝 Docling Markdown输出选项")
    print("=" * 50)
    
    # 自定义Markdown选项
    markdown_options = {
        "image_placeholder": "📷 [图像]",  # 自定义图像占位符
        "strict_text": True,              # 严格文本模式
    }
    
    try:
        parser = PDFParser(parser="docling")
        
        outputs = parser.run(
            "A.pdf", 
            modalities=["text"], 
            markdown_options=markdown_options
        )
        
        if outputs:
            result = outputs[0]
            
            # 保存自定义Markdown
            with open("docling_custom_markdown.md", "w", encoding="utf-8") as f:
                f.write(f"# 自定义Markdown输出\n\n")
                f.write(result.text.text if result.text else "")
            
            print(f"✅ 自定义Markdown已保存: docling_custom_markdown.md")
        
    except Exception as e:
        print(f"❌ Markdown选项错误: {str(e)}")

def docling_performance_comparison():
    """Docling性能对比"""
    
    print("\n⚡ Docling vs PyMuPDF 性能对比")
    print("=" * 50)
    
    import time
    
    # 测试PyMuPDF
    print("🔄 测试PyMuPDF...")
    start_time = time.time()
    
    try:
        parser_pymupdf = PDFParser(parser="pymupdf")
        outputs_pymupdf = parser_pymupdf.run("A.pdf")
        pymupdf_time = time.time() - start_time
        
        print(f"✅ PyMuPDF完成: {pymupdf_time:.2f}秒")
        
    except Exception as e:
        print(f"❌ PyMuPDF错误: {str(e)}")
        pymupdf_time = 0
    
    # 测试Docling快速模式
    print("🔄 测试Docling快速模式...")
    start_time = time.time()
    
    try:
        fast_options = PdfPipelineOptions(
            do_ocr=False,
            do_table_structure=True,
            table_structure_options=TableStructureOptions(mode=TableFormerMode.FAST),
            generate_picture_images=False
        )
        
        parser_docling_fast = PDFParser(
            parser="docling", 
            parser_kwargs={"pipeline_options": fast_options}
        )
        outputs_docling_fast = parser_docling_fast.run("A.pdf")
        docling_fast_time = time.time() - start_time
        
        print(f"✅ Docling快速模式完成: {docling_fast_time:.2f}秒")
        
    except Exception as e:
        print(f"❌ Docling快速模式错误: {str(e)}")
        docling_fast_time = 0
    
    # 显示对比结果
    if pymupdf_time > 0 and docling_fast_time > 0:
        print(f"\n📊 性能对比:")
        print(f"   PyMuPDF:      {pymupdf_time:.2f}秒")
        print(f"   Docling快速:  {docling_fast_time:.2f}秒")
        print(f"   速度比:       {docling_fast_time/pymupdf_time:.1f}x")

def create_docling_best_practices():
    """创建Docling最佳实践指南"""
    
    best_practices = """# Docling 最佳实践指南

## 🎯 选择合适的配置

### 1. 文档类型选择
- **简单文档**: 禁用OCR，使用快速表格模式
- **扫描文档**: 启用OCR，使用EasyOCR
- **复杂表格**: 启用高精度表格模式
- **多语言文档**: 配置相应的OCR语言

### 2. 性能优化
```python
# 快速模式 - 适合批量处理
fast_options = PdfPipelineOptions(
    do_ocr=False,
    do_table_structure=True,
    table_structure_options=TableStructureOptions(mode=TableFormerMode.FAST),
    generate_picture_images=False
)

# 高质量模式 - 适合重要文档
quality_options = PdfPipelineOptions(
    do_ocr=True,
    do_table_structure=True,
    table_structure_options=TableStructureOptions(mode=TableFormerMode.ACCURATE),
    ocr_options=EasyOcrOptions(force_full_page_ocr=True),
    images_scale=2.0
)
```

### 3. 内存管理
- 大文件处理时设置 `max_num_pages` 限制
- 批量处理时分批进行
- 不需要图像时设置 `generate_picture_images=False`

### 4. 错误处理
```python
try:
    outputs = parser.run(pdf_path, modalities=["text", "tables"])
except Exception as e:
    print(f"解析失败: {e}")
    # 尝试降级到PyMuPDF
    fallback_parser = PDFParser(parser="pymupdf")
    outputs = fallback_parser.run(pdf_path)
```

## 🔧 常见问题解决

### 1. OCR模型下载慢
- 使用国内镜像源
- 预先下载模型文件
- 考虑使用Tesseract替代EasyOCR

### 2. 表格识别不准确
- 尝试调整 `images_scale` 参数
- 启用 `do_cell_matching`
- 使用 `TableFormerMode.ACCURATE` 模式

### 3. 内存占用过高
- 减少 `images_scale` 值
- 禁用不需要的功能
- 分批处理大文件

## 💡 推荐配置

### 中文文档处理
```python
chinese_options = PdfPipelineOptions(
    do_ocr=True,
    ocr_options=EasyOcrOptions(
        lang=['ch_sim', 'en'],
        force_full_page_ocr=True
    ),
    do_table_structure=True,
    table_structure_options=TableStructureOptions(
        mode=TableFormerMode.ACCURATE
    )
)
```

### 表格密集型文档
```python
table_focused_options = PdfPipelineOptions(
    do_ocr=False,  # 如果是数字化PDF
    do_table_structure=True,
    table_structure_options=TableStructureOptions(
        do_cell_matching=True,
        mode=TableFormerMode.ACCURATE
    ),
    images_scale=1.5
)
```
"""
    
    with open("docling_best_practices.md", "w", encoding="utf-8") as f:
        f.write(best_practices)
    
    print(f"📚 最佳实践指南已创建: docling_best_practices.md")

def main():
    """主函数"""
    
    print("🎯 Docling 使用指南和演示")
    print("=" * 60)
    
    # 检查PDF文件
    if not os.path.exists("A.pdf"):
        print("❌ 错误: 找不到 A.pdf 文件")
        return
    
    # 基本使用
    basic_docling_usage()
    
    # 高级配置
    advanced_docling_configuration()
    
    # OCR选项
    docling_ocr_options()
    
    # Markdown选项
    docling_markdown_options()
    
    # 性能对比
    docling_performance_comparison()
    
    # 创建最佳实践指南
    create_docling_best_practices()
    
    print(f"\n🎉 Docling演示完成!")
    print(f"📁 生成的文件:")
    
    files = [
        "docling_basic_output.md",
        "docling_fast_text.txt", 
        "docling_custom_markdown.md",
        "docling_best_practices.md"
    ]
    
    for file in files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"   - {file} ({size:,} 字节)")

if __name__ == "__main__":
    main()
