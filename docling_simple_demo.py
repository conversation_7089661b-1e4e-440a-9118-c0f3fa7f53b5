#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Docling 简化演示 - 避免模型下载
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from parsestudio.parse import PDFParser

# 导入Docling相关配置
from docling.datamodel.pipeline_options import (
    PdfPipelineOptions, 
    TableStructureOptions, 
    TableFormerMode
)

def simple_docling_demo():
    """简化的Docling演示 - 不使用OCR"""
    
    print("🚀 Docling 简化演示")
    print("=" * 50)
    print("注意: 此演示不使用OCR功能，避免模型下载")
    print()
    
    # 配置不使用OCR的选项
    simple_options = PdfPipelineOptions(
        do_ocr=False,                   # 禁用OCR避免下载模型
        do_table_structure=True,        # 启用表格结构识别
        table_structure_options=TableStructureOptions(
            do_cell_matching=False,     # 简化单元格匹配
            mode=TableFormerMode.FAST   # 使用快速模式
        ),
        images_scale=1.0,               # 标准图像尺寸
        generate_picture_images=True    # 生成图像
    )
    
    try:
        print("🔧 初始化Docling解析器...")
        parser = PDFParser(
            parser="docling", 
            parser_kwargs={"pipeline_options": simple_options}
        )
        
        print("📖 开始解析PDF文件...")
        outputs = parser.run("A.pdf", modalities=["text", "tables", "images"])
        
        if outputs:
            result = outputs[0]
            
            print("✅ 解析成功!")
            print(f"   文本长度: {len(result.text.text) if result.text else 0:,} 字符")
            print(f"   表格数量: {len(result.tables) if result.tables else 0}")
            print(f"   图像数量: {len(result.images) if result.images else 0}")
            
            # 保存结果
            output_file = "docling_simple_result.md"
            with open(output_file, "w", encoding="utf-8") as f:
                f.write("# Docling 简化演示结果\n\n")
                f.write("*解析器: Docling (无OCR模式)*\n\n")
                f.write("---\n\n")
                
                if result.text:
                    f.write("## 📄 文档内容\n\n")
                    f.write(result.text.text)
                    f.write("\n\n")
                
                if result.tables:
                    f.write("## 📊 表格数据\n\n")
                    for i, table in enumerate(result.tables, 1):
                        f.write(f"### 表格 {i}\n\n")
                        if table.markdown:
                            f.write(table.markdown)
                            f.write("\n\n")
                        f.write("---\n\n")
            
            print(f"📄 结果已保存到: {output_file}")
            
            # 保存表格到CSV
            if result.tables:
                print(f"💾 保存表格到CSV文件...")
                for i, table in enumerate(result.tables, 1):
                    if table.dataframe is not None and not table.dataframe.empty:
                        csv_file = f"docling_table_{i}.csv"
                        table.dataframe.to_csv(csv_file, index=False, encoding="utf-8")
                        print(f"   表格 {i}: {csv_file}")
            
            # 保存图像
            if result.images:
                print(f"🖼️ 保存图像文件...")
                image_dir = "docling_images"
                os.makedirs(image_dir, exist_ok=True)
                
                for i, image_element in enumerate(result.images, 1):
                    image_file = os.path.join(image_dir, f"docling_image_{i}.png")
                    image_element.image.save(image_file)
                    print(f"   图像 {i}: {image_file}")
            
            return True
            
        else:
            print("❌ 解析失败: 没有返回结果")
            return False
            
    except Exception as e:
        print(f"❌ 解析过程中出现错误: {str(e)}")
        print(f"错误类型: {type(e).__name__}")
        return False

def compare_with_pymupdf():
    """与PyMuPDF对比"""
    
    print("\n🔄 与PyMuPDF对比")
    print("=" * 50)
    
    import time
    
    # 测试PyMuPDF
    print("📊 测试PyMuPDF...")
    start_time = time.time()
    
    try:
        parser_pymupdf = PDFParser(parser="pymupdf")
        outputs_pymupdf = parser_pymupdf.run("A.pdf")
        pymupdf_time = time.time() - start_time
        
        pymupdf_result = outputs_pymupdf[0] if outputs_pymupdf else None
        
        print(f"✅ PyMuPDF完成: {pymupdf_time:.2f}秒")
        if pymupdf_result:
            print(f"   文本长度: {len(pymupdf_result.text.text) if pymupdf_result.text else 0:,}")
            print(f"   表格数量: {len(pymupdf_result.tables) if pymupdf_result.tables else 0}")
            print(f"   图像数量: {len(pymupdf_result.images) if pymupdf_result.images else 0}")
        
    except Exception as e:
        print(f"❌ PyMuPDF错误: {str(e)}")
        pymupdf_time = 0
        pymupdf_result = None
    
    # 测试Docling
    print("\n📊 测试Docling...")
    start_time = time.time()
    
    try:
        simple_options = PdfPipelineOptions(
            do_ocr=False,
            do_table_structure=True,
            table_structure_options=TableStructureOptions(mode=TableFormerMode.FAST),
            generate_picture_images=True
        )
        
        parser_docling = PDFParser(
            parser="docling", 
            parser_kwargs={"pipeline_options": simple_options}
        )
        outputs_docling = parser_docling.run("A.pdf")
        docling_time = time.time() - start_time
        
        docling_result = outputs_docling[0] if outputs_docling else None
        
        print(f"✅ Docling完成: {docling_time:.2f}秒")
        if docling_result:
            print(f"   文本长度: {len(docling_result.text.text) if docling_result.text else 0:,}")
            print(f"   表格数量: {len(docling_result.tables) if docling_result.tables else 0}")
            print(f"   图像数量: {len(docling_result.images) if docling_result.images else 0}")
        
    except Exception as e:
        print(f"❌ Docling错误: {str(e)}")
        docling_time = 0
        docling_result = None
    
    # 显示对比结果
    print(f"\n📈 对比结果:")
    print("=" * 50)
    
    if pymupdf_time > 0 and docling_time > 0:
        print(f"⏱️ 处理时间:")
        print(f"   PyMuPDF:  {pymupdf_time:.2f}秒")
        print(f"   Docling:  {docling_time:.2f}秒")
        print(f"   速度比:   {docling_time/pymupdf_time:.1f}x")
    
    if pymupdf_result and docling_result:
        print(f"\n📊 内容提取:")
        print(f"   解析器    文本长度    表格数    图像数")
        print(f"   PyMuPDF   {len(pymupdf_result.text.text) if pymupdf_result.text else 0:8,}    {len(pymupdf_result.tables) if pymupdf_result.tables else 0:6}    {len(pymupdf_result.images) if pymupdf_result.images else 0:6}")
        print(f"   Docling   {len(docling_result.text.text) if docling_result.text else 0:8,}    {len(docling_result.tables) if docling_result.tables else 0:6}    {len(docling_result.images) if docling_result.images else 0:6}")

def show_docling_features():
    """展示Docling的特色功能"""
    
    print(f"\n🌟 Docling 特色功能")
    print("=" * 50)
    
    features = [
        {
            "name": "高级表格识别",
            "description": "使用AI模型识别复杂表格结构",
            "advantage": "比传统方法更准确"
        },
        {
            "name": "多语言OCR",
            "description": "支持多种语言的光学字符识别",
            "advantage": "处理扫描文档和图像中的文字"
        },
        {
            "name": "智能布局分析",
            "description": "理解文档的逻辑结构",
            "advantage": "保持原始格式和层次"
        },
        {
            "name": "精确位置信息",
            "description": "提供元素的精确坐标",
            "advantage": "便于后续处理和分析"
        },
        {
            "name": "多种输出格式",
            "description": "支持Markdown、JSON等格式",
            "advantage": "灵活的数据导出"
        }
    ]
    
    for i, feature in enumerate(features, 1):
        print(f"{i}. **{feature['name']}**")
        print(f"   描述: {feature['description']}")
        print(f"   优势: {feature['advantage']}")
        print()

def create_usage_summary():
    """创建使用总结"""
    
    summary = """# Docling 使用总结

## 🎯 什么是Docling？

Docling是IBM开源的高级PDF解析库，具有以下特点：
- **本地运行**: 不需要API密钥，完全本地处理
- **AI增强**: 使用机器学习模型提高解析精度
- **多模态**: 支持文本、表格、图像的综合提取
- **高精度**: 特别擅长处理复杂表格和布局

## 🔧 基本使用方法

### 1. 简单使用
```python
from parsestudio.parse import PDFParser

# 使用默认配置
parser = PDFParser(parser="docling")
outputs = parser.run("document.pdf")
```

### 2. 自定义配置
```python
from docling.datamodel.pipeline_options import PdfPipelineOptions, TableStructureOptions

# 配置选项
options = PdfPipelineOptions(
    do_ocr=True,                    # 启用OCR
    do_table_structure=True,        # 启用表格识别
    generate_picture_images=True    # 生成图像
)

parser = PDFParser(parser="docling", parser_kwargs={"pipeline_options": options})
```

## ⚡ 性能优化建议

### 快速模式（推荐用于批量处理）
```python
fast_options = PdfPipelineOptions(
    do_ocr=False,                   # 禁用OCR
    do_table_structure=True,
    table_structure_options=TableStructureOptions(mode=TableFormerMode.FAST),
    generate_picture_images=False
)
```

### 高质量模式（推荐用于重要文档）
```python
quality_options = PdfPipelineOptions(
    do_ocr=True,
    ocr_options=EasyOcrOptions(force_full_page_ocr=True),
    table_structure_options=TableStructureOptions(mode=TableFormerMode.ACCURATE),
    images_scale=2.0
)
```

## 🆚 与其他解析器对比

| 特性 | PyMuPDF | Docling | LlamaParse |
|------|---------|---------|------------|
| 速度 | 很快 | 中等 | 慢 |
| 精度 | 中等 | 高 | 很高 |
| 表格识别 | 基础 | 高级 | 最佳 |
| OCR支持 | 无 | 有 | 有 |
| 本地运行 | ✅ | ✅ | ❌ |
| 免费使用 | ✅ | ✅ | ❌ |

## 💡 使用建议

1. **简单文档**: 使用PyMuPDF，速度快
2. **复杂表格**: 使用Docling，精度高
3. **扫描文档**: 使用Docling + OCR
4. **批量处理**: 使用Docling快速模式
5. **最高精度**: 考虑LlamaParse（需付费）

## 🔧 常见问题

### Q: 首次使用很慢？
A: Docling需要下载AI模型，首次使用会比较慢，后续使用会很快。

### Q: 如何处理中文文档？
A: 启用OCR并配置中文语言支持：
```python
ocr_options=EasyOcrOptions(lang=['ch_sim', 'en'])
```

### Q: 内存占用过高？
A: 减少图像尺寸或禁用不需要的功能：
```python
options = PdfPipelineOptions(
    images_scale=1.0,
    generate_picture_images=False
)
```
"""
    
    with open("docling_usage_summary.md", "w", encoding="utf-8") as f:
        f.write(summary)
    
    print(f"📚 使用总结已创建: docling_usage_summary.md")

def main():
    """主函数"""
    
    print("🎯 Docling 简化演示")
    print("=" * 60)
    
    # 检查PDF文件
    if not os.path.exists("A.pdf"):
        print("❌ 错误: 找不到 A.pdf 文件")
        return
    
    # 简化演示
    success = simple_docling_demo()
    
    if success:
        # 性能对比
        compare_with_pymupdf()
        
        # 展示特色功能
        show_docling_features()
        
        # 创建使用总结
        create_usage_summary()
        
        print(f"\n🎉 Docling演示完成!")
        print(f"📁 生成的文件:")
        
        files = [
            "docling_simple_result.md",
            "docling_usage_summary.md"
        ]
        
        for file in files:
            if os.path.exists(file):
                size = os.path.getsize(file)
                print(f"   - {file} ({size:,} 字节)")
        
        # 检查CSV和图像文件
        csv_files = [f for f in os.listdir(".") if f.startswith("docling_table_") and f.endswith(".csv")]
        if csv_files:
            print(f"   📊 表格文件: {len(csv_files)} 个CSV文件")
        
        if os.path.exists("docling_images"):
            image_files = os.listdir("docling_images")
            if image_files:
                print(f"   🖼️ 图像文件: {len(image_files)} 个图像文件")
    
    else:
        print(f"\n💡 如果Docling遇到问题，可以:")
        print("1. 等待模型下载完成")
        print("2. 检查网络连接")
        print("3. 使用PyMuPDF作为替代方案")

if __name__ == "__main__":
    main()
