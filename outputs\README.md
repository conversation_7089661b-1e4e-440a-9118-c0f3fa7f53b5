# PDF解析结果目录结构

## 📁 目录说明

### outputs/ - 解析结果根目录
- **pymupdf/** - PyMuPDF解析器结果
- **docling/** - Docling解析器结果  
- **llama/** - LlamaParse解析器结果
- **anthropic/** - Anthropic解析器结果
- **comparison/** - 解析器对比结果

### 每个解析器目录包含：
- **text/** - 提取的文本文件
- **tables/** - 表格文件（CSV和Markdown格式）
- **images/** - 提取的图像文件
- **markdown/** - 格式化的Markdown文档

### scripts/ - 解析脚本
包含各种PDF解析和处理脚本

### docs/ - 文档和指南
包含使用指南和最佳实践文档

## 🚀 使用方法

1. 将PDF文件放在项目根目录
2. 运行相应的解析脚本
3. 查看outputs目录中的结果

## 📊 文件命名规范

- 文本文件: `{pdf_name}_text.txt`
- 表格文件: `{pdf_name}_table_{n}.csv/md`
- 图像文件: `{pdf_name}_image_{n}_page_{p}.png`
- Markdown文件: `{pdf_name}_{parser}_formatted.md`
