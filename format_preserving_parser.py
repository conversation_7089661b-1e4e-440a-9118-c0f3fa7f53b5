#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
格式保持的PDF解析器
分别使用PyMuPDF和Docling解析PDF，生成完全保持原始格式的Markdown文件
"""

import os
import sys
import re
from pathlib import Path
from datetime import datetime

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from parsestudio.parse import PDFParser
from docling.datamodel.pipeline_options import (
    PdfPipelineOptions,
    TableStructureOptions,
    TableFormerMode
)

class FormatPreservingParser:
    """格式保持的PDF解析器"""

    def __init__(self):
        self.output_dir = "outputs"
        os.makedirs(self.output_dir, exist_ok=True)

    def clean_and_format_text(self, text):
        """清理和格式化文本，保持原始结构"""
        if not text:
            return ""

        # 分割成行
        lines = text.split('\n')
        formatted_lines = []

        for line in lines:
            line = line.strip()

            # 跳过空行但保留段落分隔
            if not line:
                if formatted_lines and formatted_lines[-1] != "":
                    formatted_lines.append("")
                continue

            # 检测标题（简单启发式）
            if self.is_title(line):
                # 添加标题格式
                if len(line) < 20:
                    formatted_lines.append(f"## {line}")
                elif len(line) < 40:
                    formatted_lines.append(f"### {line}")
                else:
                    formatted_lines.append(f"**{line}**")
            else:
                formatted_lines.append(line)

        return '\n'.join(formatted_lines)

    def is_title(self, line):
        """判断是否为标题"""
        if len(line) > 100:
            return False

        # 标题特征
        title_indicators = [
            '检测报告', '声明', '基本信息', '检测项目', '情况汇总',
            '建筑物基本情况', '接闪器', '引下线', '接地装置',
            '防雷等电位连接', '电涌保护器', '磁屏蔽'
        ]

        for indicator in title_indicators:
            if indicator in line:
                return True

        # 检查是否全是大写字母或包含特殊符号
        if line.isupper() or '：' in line or '=' in line:
            return True

        return False

    def format_table_markdown(self, table_element):
        """格式化表格为更好的Markdown"""
        if not table_element.markdown:
            return ""

        # 获取原始markdown
        markdown = table_element.markdown

        # 添加表格说明
        result = []
        if table_element.metadata and table_element.metadata.page_number:
            result.append(f"*表格 - 第{table_element.metadata.page_number}页*")
            result.append("")

        # 清理表格格式
        lines = markdown.split('\n')
        cleaned_lines = []

        for line in lines:
            line = line.strip()
            if line and '|' in line:
                # 清理表格行
                cells = [cell.strip() for cell in line.split('|')]
                # 移除空的首尾元素
                if cells and not cells[0]:
                    cells = cells[1:]
                if cells and not cells[-1]:
                    cells = cells[:-1]

                if cells:
                    cleaned_line = '| ' + ' | '.join(cells) + ' |'
                    cleaned_lines.append(cleaned_line)

        result.extend(cleaned_lines)
        result.append("")

        return '\n'.join(result)

    def create_comprehensive_markdown(self, result, pdf_name, parser_name):
        """创建完整的格式保持Markdown"""
        content = []

        # 文档头部
        content.extend([
            f"# {pdf_name}",
            "",
            f"**解析器**: {parser_name.upper()}",
            f"**解析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "---",
            ""
        ])

        # 统计信息
        text_length = len(result.text.text) if result.text and result.text.text else 0
        table_count = len(result.tables) if result.tables else 0
        image_count = len(result.images) if result.images else 0

        content.extend([
            "## 📊 文档信息",
            "",
            f"- 📄 **文本长度**: {text_length:,} 字符",
            f"- 📊 **表格数量**: {table_count} 个",
            f"- 🖼️ **图像数量**: {image_count} 个",
            f"- 🔧 **解析工具**: {parser_name.upper()}",
            "",
            "---",
            ""
        ])

        # 主要内容 - 保持原始格式
        if result.text and result.text.text:
            # 清理和格式化文本
            formatted_text = self.clean_and_format_text(result.text.text)

            # 按页面或章节分割（如果可能）
            sections = self.split_into_sections(formatted_text)

            for i, section in enumerate(sections):
                if section.strip():
                    content.extend([
                        section,
                        "",
                        "---",
                        ""
                    ])

        # 表格部分 - 保持原始格式
        if result.tables:
            content.extend([
                "## 📊 详细表格数据",
                ""
            ])

            for i, table in enumerate(result.tables, 1):
                formatted_table = self.format_table_markdown(table)
                if formatted_table:
                    content.extend([
                        f"### 表格 {i}",
                        "",
                        formatted_table,
                        "---",
                        ""
                    ])

        # 图像部分
        if result.images:
            content.extend([
                "## 🖼️ 文档图像",
                ""
            ])

            for i, image_element in enumerate(result.images, 1):
                page_num = image_element.metadata.page_number if image_element.metadata else i
                content.extend([
                    f"### 图像 {i} (第{page_num}页)",
                    "",
                    f"![图像{i}](images/{pdf_name}_image_{i}_page_{page_num}.png)",
                    "",
                    "---",
                    ""
                ])

        # 文档尾部
        content.extend([
            "---",
            "",
            f"*本文档由 {parser_name.upper()} 解析器自动生成，力求保持原始PDF格式*",
            "",
            f"*生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*"
        ])

        return '\n'.join(content)

    def split_into_sections(self, text):
        """将文本分割成逻辑章节"""
        # 按照常见的分隔符分割
        section_markers = [
            '检测报告', '声明', '基本信息', '检测项目情况汇总表',
            '建筑物基本情况', '接闪器', '引下线', '接地装置',
            '防雷等电位连接', '电涌保护器', '磁屏蔽'
        ]

        sections = []
        current_section = []

        lines = text.split('\n')

        for line in lines:
            # 检查是否是新章节的开始
            is_new_section = False
            for marker in section_markers:
                if marker in line and len(line) < 50:
                    is_new_section = True
                    break

            if is_new_section and current_section:
                # 保存当前章节
                sections.append('\n'.join(current_section))
                current_section = [line]
            else:
                current_section.append(line)

        # 添加最后一个章节
        if current_section:
            sections.append('\n'.join(current_section))

        return sections

    def parse_with_pymupdf(self, pdf_path):
        """使用PyMuPDF解析并保持格式"""
        print("🔄 使用 PyMuPDF 解析PDF...")

        try:
            parser = PDFParser(parser="pymupdf")
            outputs = parser.run(pdf_path, modalities=["text", "tables", "images"])

            if outputs:
                result = outputs[0]
                pdf_name = os.path.splitext(os.path.basename(pdf_path))[0]

                # 创建输出目录
                pymupdf_dir = os.path.join(self.output_dir, "pymupdf")
                images_dir = os.path.join(pymupdf_dir, "images")
                os.makedirs(images_dir, exist_ok=True)

                # 保存图像
                if result.images:
                    for i, image_element in enumerate(result.images, 1):
                        page_num = image_element.metadata.page_number if image_element.metadata else i
                        image_file = os.path.join(images_dir, f"{pdf_name}_image_{i}_page_{page_num}.png")
                        image_element.image.save(image_file)

                # 生成格式保持的Markdown
                markdown_content = self.create_comprehensive_markdown(result, pdf_name, "pymupdf")

                # 保存Markdown文件
                markdown_file = os.path.join(pymupdf_dir, f"{pdf_name}_pymupdf_formatted.md")
                with open(markdown_file, "w", encoding="utf-8") as f:
                    f.write(markdown_content)

                print(f"✅ PyMuPDF 解析完成")
                print(f"   📄 文本长度: {len(result.text.text) if result.text else 0:,} 字符")
                print(f"   📊 表格数量: {len(result.tables) if result.tables else 0}")
                print(f"   🖼️ 图像数量: {len(result.images) if result.images else 0}")
                print(f"   💾 保存位置: {markdown_file}")

                return markdown_file, result

        except Exception as e:
            print(f"❌ PyMuPDF 解析失败: {e}")
            return None, None

    def parse_with_docling(self, pdf_path):
        """使用Docling解析并保持格式"""
        print("\n🔄 使用 Docling 解析PDF...")

        try:
            # 配置Docling选项（快速模式，保持格式）
            options = PdfPipelineOptions(
                do_ocr=False,  # 禁用OCR以保持原始文本格式
                do_table_structure=True,
                table_structure_options=TableStructureOptions(mode=TableFormerMode.FAST),
                generate_picture_images=True,
                images_scale=1.0
            )

            parser = PDFParser(parser="docling", parser_kwargs={"pipeline_options": options})
            outputs = parser.run(pdf_path, modalities=["text", "tables", "images"])

            if outputs:
                result = outputs[0]
                pdf_name = os.path.splitext(os.path.basename(pdf_path))[0]

                # 创建输出目录
                docling_dir = os.path.join(self.output_dir, "docling")
                images_dir = os.path.join(docling_dir, "images")
                os.makedirs(images_dir, exist_ok=True)

                # 保存图像
                if result.images:
                    for i, image_element in enumerate(result.images, 1):
                        page_num = image_element.metadata.page_number if image_element.metadata else i
                        image_file = os.path.join(images_dir, f"{pdf_name}_image_{i}_page_{page_num}.png")
                        image_element.image.save(image_file)

                # 生成格式保持的Markdown
                markdown_content = self.create_comprehensive_markdown(result, pdf_name, "docling")

                # 保存Markdown文件
                markdown_file = os.path.join(docling_dir, f"{pdf_name}_docling_formatted.md")
                with open(markdown_file, "w", encoding="utf-8") as f:
                    f.write(markdown_content)

                print(f"✅ Docling 解析完成")
                print(f"   📄 文本长度: {len(result.text.text) if result.text else 0:,} 字符")
                print(f"   📊 表格数量: {len(result.tables) if result.tables else 0}")
                print(f"   🖼️ 图像数量: {len(result.images) if result.images else 0}")
                print(f"   💾 保存位置: {markdown_file}")

                return markdown_file, result

        except Exception as e:
            print(f"❌ Docling 解析失败: {e}")
            return None, None

    def create_comparison_report(self, pymupdf_result, docling_result, pdf_name):
        """创建对比报告"""
        print("\n📊 生成对比报告...")

        comparison_dir = os.path.join(self.output_dir, "comparison")
        os.makedirs(comparison_dir, exist_ok=True)

        # 统计信息
        pymupdf_stats = {
            'text_length': len(pymupdf_result.text.text) if pymupdf_result and pymupdf_result.text else 0,
            'table_count': len(pymupdf_result.tables) if pymupdf_result and pymupdf_result.tables else 0,
            'image_count': len(pymupdf_result.images) if pymupdf_result and pymupdf_result.images else 0
        }

        docling_stats = {
            'text_length': len(docling_result.text.text) if docling_result and docling_result.text else 0,
            'table_count': len(docling_result.tables) if docling_result and docling_result.tables else 0,
            'image_count': len(docling_result.images) if docling_result and docling_result.images else 0
        }

        # 生成对比报告
        report_content = [
            f"# PDF解析对比报告 - {pdf_name}",
            "",
            f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "## 📊 解析结果对比",
            "",
            "| 指标 | PyMuPDF | Docling | 差异 |",
            "|------|---------|---------|------|",
            f"| 文本长度 | {pymupdf_stats['text_length']:,} 字符 | {docling_stats['text_length']:,} 字符 | {docling_stats['text_length'] - pymupdf_stats['text_length']:+,} |",
            f"| 表格数量 | {pymupdf_stats['table_count']} 个 | {docling_stats['table_count']} 个 | {docling_stats['table_count'] - pymupdf_stats['table_count']:+} |",
            f"| 图像数量 | {pymupdf_stats['image_count']} 个 | {docling_stats['image_count']} 个 | {docling_stats['image_count'] - pymupdf_stats['image_count']:+} |",
            "",
            "## 🎯 格式保持效果",
            "",
            "### PyMuPDF",
            "- ✅ 快速解析",
            "- ✅ 基础格式保持",
            "- ⚠️ 表格结构相对简单",
            "",
            "### Docling",
            "- ✅ 高质量解析",
            "- ✅ 优秀的表格结构识别",
            "- ✅ 更好的布局理解",
            "- ⚠️ 处理时间较长",
            "",
            "## 💡 使用建议",
            "",
            "1. **快速预览**: 使用 PyMuPDF 结果",
            "2. **高质量输出**: 使用 Docling 结果",
            "3. **表格密集文档**: 推荐 Docling",
            "4. **简单文档**: PyMuPDF 已足够",
            "",
            "## 📁 文件位置",
            "",
            f"- PyMuPDF 结果: `outputs/pymupdf/{pdf_name}_pymupdf_formatted.md`",
            f"- Docling 结果: `outputs/docling/{pdf_name}_docling_formatted.md`",
            "",
            "---",
            "",
            "*此报告由格式保持解析器自动生成*"
        ]

        report_file = os.path.join(comparison_dir, f"{pdf_name}_comparison_report.md")
        with open(report_file, "w", encoding="utf-8") as f:
            f.write('\n'.join(report_content))

        print(f"✅ 对比报告已生成: {report_file}")
        return report_file

def main():
    """主函数"""
    print("🎯 格式保持的PDF解析工具")
    print("=" * 60)
    print("目标: 完全按照原始PDF格式生成Markdown文件")
    print()

    # 检查PDF文件
    pdf_file = "A.pdf"
    if not os.path.exists(pdf_file):
        print(f"❌ 错误: 找不到 {pdf_file} 文件")
        return

    # 创建解析器
    parser = FormatPreservingParser()

    # 使用PyMuPDF解析
    pymupdf_file, pymupdf_result = parser.parse_with_pymupdf(pdf_file)

    # 使用Docling解析
    docling_file, docling_result = parser.parse_with_docling(pdf_file)

    # 生成对比报告
    if pymupdf_result or docling_result:
        pdf_name = os.path.splitext(os.path.basename(pdf_file))[0]
        comparison_file = parser.create_comparison_report(pymupdf_result, docling_result, pdf_name)

    print(f"\n🎉 格式保持解析完成!")
    print(f"📁 所有结果已保存到 outputs/ 目录")
    print(f"📊 查看对比报告了解详细差异")

    print(f"\n📋 生成的文件:")
    if pymupdf_file:
        print(f"   - PyMuPDF: {pymupdf_file}")
    if docling_file:
        print(f"   - Docling: {docling_file}")
    if 'comparison_file' in locals():
        print(f"   - 对比报告: {comparison_file}")

if __name__ == "__main__":
    main()
