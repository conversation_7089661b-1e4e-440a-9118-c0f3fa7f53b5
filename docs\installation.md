# Installation Guide

This guide will help you install and set up the Parsestudio library to start parsing PDFs with ease.

---

## Requirements

Before installing Parsestudio, ensure that the following requirements are met:

- Python 3.12 or later
- pip (Python package manager)

---

## Installing Parsestudio

### 1. Using pip

You can install Parsestudio directly from PyPI using pip:

```bash
pip install parsestudio
```
This will install the latest stable version of the library along with its dependencies.

### 2. From Source
If you prefer to install the library from the source code:

1. Clone the Parsestudio repository from GitHub:

```bash
git clone https://github.com/chatclimate-ai/ParseStudio.git
cd ParseStudio
```

2. Install the library using pip:

```bash
pip install .
```

Nou're now ready to use Parsestudio! 🎉
