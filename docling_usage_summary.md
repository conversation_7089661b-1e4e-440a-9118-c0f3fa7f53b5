# Docling 使用总结

## 🎯 什么是Docling？

Docling是IBM开源的高级PDF解析库，具有以下特点：
- **本地运行**: 不需要API密钥，完全本地处理
- **AI增强**: 使用机器学习模型提高解析精度
- **多模态**: 支持文本、表格、图像的综合提取
- **高精度**: 特别擅长处理复杂表格和布局

## 🔧 基本使用方法

### 1. 简单使用
```python
from parsestudio.parse import PDFParser

# 使用默认配置
parser = PDFParser(parser="docling")
outputs = parser.run("document.pdf")
```

### 2. 自定义配置
```python
from docling.datamodel.pipeline_options import PdfPipelineOptions, TableStructureOptions

# 配置选项
options = PdfPipelineOptions(
    do_ocr=True,                    # 启用OCR
    do_table_structure=True,        # 启用表格识别
    generate_picture_images=True    # 生成图像
)

parser = PDFParser(parser="docling", parser_kwargs={"pipeline_options": options})
```

## ⚡ 性能优化建议

### 快速模式（推荐用于批量处理）
```python
fast_options = PdfPipelineOptions(
    do_ocr=False,                   # 禁用OCR
    do_table_structure=True,
    table_structure_options=TableStructureOptions(mode=TableFormerMode.FAST),
    generate_picture_images=False
)
```

### 高质量模式（推荐用于重要文档）
```python
quality_options = PdfPipelineOptions(
    do_ocr=True,
    ocr_options=EasyOcrOptions(force_full_page_ocr=True),
    table_structure_options=TableStructureOptions(mode=TableFormerMode.ACCURATE),
    images_scale=2.0
)
```

## 🆚 与其他解析器对比

| 特性 | PyMuPDF | Docling | LlamaParse |
|------|---------|---------|------------|
| 速度 | 很快 | 中等 | 慢 |
| 精度 | 中等 | 高 | 很高 |
| 表格识别 | 基础 | 高级 | 最佳 |
| OCR支持 | 无 | 有 | 有 |
| 本地运行 | ✅ | ✅ | ❌ |
| 免费使用 | ✅ | ✅ | ❌ |

## 💡 使用建议

1. **简单文档**: 使用PyMuPDF，速度快
2. **复杂表格**: 使用Docling，精度高
3. **扫描文档**: 使用Docling + OCR
4. **批量处理**: 使用Docling快速模式
5. **最高精度**: 考虑LlamaParse（需付费）

## 🔧 常见问题

### Q: 首次使用很慢？
A: Docling需要下载AI模型，首次使用会比较慢，后续使用会很快。

### Q: 如何处理中文文档？
A: 启用OCR并配置中文语言支持：
```python
ocr_options=EasyOcrOptions(lang=['ch_sim', 'en'])
```

### Q: 内存占用过高？
A: 减少图像尺寸或禁用不需要的功能：
```python
options = PdfPipelineOptions(
    images_scale=1.0,
    generate_picture_images=False
)
```
