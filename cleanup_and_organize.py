#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理和重新组织PDF解析文件
将不同工具的解析结果存放到指定目录中
"""

import os
import shutil
import glob
from pathlib import Path

def cleanup_old_files():
    """清理之前生成的解析文件"""
    
    print("🧹 开始清理之前生成的文件...")
    
    # 要清理的文件模式
    cleanup_patterns = [
        # Markdown文件
        "A_*.md",
        "*_formatted.md", 
        "*_demo.md",
        "*_result.md",
        "*_summary.md",
        "docling_*.md",
        
        # 文本文件
        "*.txt",
        
        # CSV文件
        "A_table_*.csv",
        "docling_table_*.csv",
        
        # Python脚本（保留主要脚本）
        "parse_a_pdf*.py",
        "demo_*.py",
        "docling_*.py",
        "sample_usage.py",
    ]
    
    # 要清理的目录
    cleanup_dirs = [
        "parsed_output",
        "parsed_output_simple", 
        "A_*_images",
        "docling_images",
    ]
    
    cleaned_files = 0
    cleaned_dirs = 0
    
    # 清理文件
    for pattern in cleanup_patterns:
        files = glob.glob(pattern)
        for file in files:
            try:
                os.remove(file)
                print(f"  ✓ 删除文件: {file}")
                cleaned_files += 1
            except Exception as e:
                print(f"  ❌ 删除文件失败 {file}: {e}")
    
    # 清理目录
    for dir_pattern in cleanup_dirs:
        dirs = glob.glob(dir_pattern)
        for dir_path in dirs:
            if os.path.isdir(dir_path):
                try:
                    shutil.rmtree(dir_path)
                    print(f"  ✓ 删除目录: {dir_path}")
                    cleaned_dirs += 1
                except Exception as e:
                    print(f"  ❌ 删除目录失败 {dir_path}: {e}")
    
    print(f"🎉 清理完成! 删除了 {cleaned_files} 个文件和 {cleaned_dirs} 个目录")
    return cleaned_files, cleaned_dirs

def create_directory_structure():
    """创建新的目录结构"""
    
    print("\n📁 创建新的目录结构...")
    
    # 定义目录结构
    directories = {
        "outputs": "所有解析结果的根目录",
        "outputs/pymupdf": "PyMuPDF解析结果",
        "outputs/pymupdf/text": "文本文件",
        "outputs/pymupdf/tables": "表格文件", 
        "outputs/pymupdf/images": "图像文件",
        "outputs/pymupdf/markdown": "Markdown文件",
        
        "outputs/docling": "Docling解析结果",
        "outputs/docling/text": "文本文件",
        "outputs/docling/tables": "表格文件",
        "outputs/docling/images": "图像文件", 
        "outputs/docling/markdown": "Markdown文件",
        
        "outputs/llama": "LlamaParse解析结果",
        "outputs/llama/text": "文本文件",
        "outputs/llama/tables": "表格文件",
        "outputs/llama/images": "图像文件",
        "outputs/llama/markdown": "Markdown文件",
        
        "outputs/anthropic": "Anthropic解析结果", 
        "outputs/anthropic/text": "文本文件",
        "outputs/anthropic/tables": "表格文件",
        "outputs/anthropic/images": "图像文件",
        "outputs/anthropic/markdown": "Markdown文件",
        
        "outputs/comparison": "解析器对比结果",
        "scripts": "解析脚本",
        "docs": "文档和指南"
    }
    
    created_dirs = 0
    
    for dir_path, description in directories.items():
        try:
            os.makedirs(dir_path, exist_ok=True)
            print(f"  ✓ 创建目录: {dir_path} ({description})")
            created_dirs += 1
        except Exception as e:
            print(f"  ❌ 创建目录失败 {dir_path}: {e}")
    
    # 创建README文件
    readme_content = """# PDF解析结果目录结构

## 📁 目录说明

### outputs/ - 解析结果根目录
- **pymupdf/** - PyMuPDF解析器结果
- **docling/** - Docling解析器结果  
- **llama/** - LlamaParse解析器结果
- **anthropic/** - Anthropic解析器结果
- **comparison/** - 解析器对比结果

### 每个解析器目录包含：
- **text/** - 提取的文本文件
- **tables/** - 表格文件（CSV和Markdown格式）
- **images/** - 提取的图像文件
- **markdown/** - 格式化的Markdown文档

### scripts/ - 解析脚本
包含各种PDF解析和处理脚本

### docs/ - 文档和指南
包含使用指南和最佳实践文档

## 🚀 使用方法

1. 将PDF文件放在项目根目录
2. 运行相应的解析脚本
3. 查看outputs目录中的结果

## 📊 文件命名规范

- 文本文件: `{pdf_name}_text.txt`
- 表格文件: `{pdf_name}_table_{n}.csv/md`
- 图像文件: `{pdf_name}_image_{n}_page_{p}.png`
- Markdown文件: `{pdf_name}_{parser}_formatted.md`
"""
    
    try:
        with open("outputs/README.md", "w", encoding="utf-8") as f:
            f.write(readme_content)
        print(f"  ✓ 创建说明文件: outputs/README.md")
    except Exception as e:
        print(f"  ❌ 创建说明文件失败: {e}")
    
    print(f"🎉 目录结构创建完成! 创建了 {created_dirs} 个目录")
    return created_dirs

def create_organized_parser_script():
    """创建组织化的解析脚本"""
    
    print("\n📝 创建组织化的解析脚本...")
    
    script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
组织化的PDF解析脚本
将不同解析器的结果存放到指定目录
"""

import os
import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from parsestudio.parse import PDFParser
from docling.datamodel.pipeline_options import (
    PdfPipelineOptions, 
    TableStructureOptions, 
    TableFormerMode, 
    EasyOcrOptions
)

class OrganizedPDFParser:
    """组织化的PDF解析器"""
    
    def __init__(self, base_output_dir="outputs"):
        self.base_output_dir = base_output_dir
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    def _create_parser_dirs(self, parser_name):
        """为指定解析器创建目录结构"""
        dirs = {
            'base': os.path.join(self.base_output_dir, parser_name),
            'text': os.path.join(self.base_output_dir, parser_name, 'text'),
            'tables': os.path.join(self.base_output_dir, parser_name, 'tables'),
            'images': os.path.join(self.base_output_dir, parser_name, 'images'),
            'markdown': os.path.join(self.base_output_dir, parser_name, 'markdown')
        }
        
        for dir_path in dirs.values():
            os.makedirs(dir_path, exist_ok=True)
        
        return dirs
    
    def _save_results(self, result, pdf_name, parser_name, dirs):
        """保存解析结果到指定目录"""
        saved_files = []
        
        # 保存文本
        if result.text and result.text.text:
            text_file = os.path.join(dirs['text'], f"{pdf_name}_text.txt")
            with open(text_file, "w", encoding="utf-8") as f:
                f.write(result.text.text)
            saved_files.append(text_file)
        
        # 保存表格
        if result.tables:
            for i, table in enumerate(result.tables, 1):
                # Markdown格式
                if table.markdown:
                    md_file = os.path.join(dirs['tables'], f"{pdf_name}_table_{i}.md")
                    with open(md_file, "w", encoding="utf-8") as f:
                        f.write(table.markdown)
                    saved_files.append(md_file)
                
                # CSV格式
                if table.dataframe is not None and not table.dataframe.empty:
                    csv_file = os.path.join(dirs['tables'], f"{pdf_name}_table_{i}.csv")
                    table.dataframe.to_csv(csv_file, index=False, encoding="utf-8")
                    saved_files.append(csv_file)
        
        # 保存图像
        if result.images:
            for i, image_element in enumerate(result.images, 1):
                page_num = image_element.metadata.page_number if image_element.metadata else i
                image_file = os.path.join(dirs['images'], f"{pdf_name}_image_{i}_page_{page_num}.png")
                image_element.image.save(image_file)
                saved_files.append(image_file)
        
        # 生成格式化的Markdown
        markdown_file = os.path.join(dirs['markdown'], f"{pdf_name}_{parser_name}_formatted.md")
        self._create_formatted_markdown(result, pdf_name, parser_name, markdown_file)
        saved_files.append(markdown_file)
        
        return saved_files
    
    def _create_formatted_markdown(self, result, pdf_name, parser_name, output_file):
        """创建格式化的Markdown文件"""
        content = []
        
        # 标题和元信息
        content.extend([
            f"# {pdf_name} - {parser_name.upper()} 解析结果",
            "",
            f"*解析器: {parser_name.upper()}*",
            f"*解析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*",
            "",
            "---",
            ""
        ])
        
        # 统计信息
        text_length = len(result.text.text) if result.text and result.text.text else 0
        table_count = len(result.tables) if result.tables else 0
        image_count = len(result.images) if result.images else 0
        
        content.extend([
            "## 📊 解析统计",
            "",
            f"- **文本长度**: {text_length:,} 字符",
            f"- **表格数量**: {table_count}",
            f"- **图像数量**: {image_count}",
            "",
            "---",
            ""
        ])
        
        # 文本内容
        if result.text and result.text.text:
            content.extend([
                "## 📄 文档内容",
                "",
                result.text.text,
                "",
                "---",
                ""
            ])
        
        # 表格
        if result.tables:
            content.extend([
                "## 📊 表格数据",
                ""
            ])
            
            for i, table in enumerate(result.tables, 1):
                content.extend([
                    f"### 表格 {i}",
                    ""
                ])
                
                if table.metadata and table.metadata.page_number:
                    content.extend([
                        f"*位置: 第{table.metadata.page_number}页*",
                        ""
                    ])
                
                if table.markdown:
                    content.extend([
                        table.markdown,
                        "",
                        "---",
                        ""
                    ])
        
        # 写入文件
        with open(output_file, "w", encoding="utf-8") as f:
            f.write("\\n".join(content))
    
    def parse_with_pymupdf(self, pdf_path):
        """使用PyMuPDF解析"""
        print(f"🔄 使用PyMuPDF解析 {pdf_path}...")
        
        dirs = self._create_parser_dirs("pymupdf")
        parser = PDFParser(parser="pymupdf")
        
        try:
            outputs = parser.run(pdf_path, modalities=["text", "tables", "images"])
            if outputs:
                pdf_name = os.path.splitext(os.path.basename(pdf_path))[0]
                saved_files = self._save_results(outputs[0], pdf_name, "pymupdf", dirs)
                print(f"✅ PyMuPDF解析完成，保存了 {len(saved_files)} 个文件")
                return saved_files
        except Exception as e:
            print(f"❌ PyMuPDF解析失败: {e}")
        
        return []
    
    def parse_with_docling(self, pdf_path, use_ocr=False):
        """使用Docling解析"""
        print(f"🔄 使用Docling解析 {pdf_path}...")
        
        dirs = self._create_parser_dirs("docling")
        
        # 配置选项
        if use_ocr:
            options = PdfPipelineOptions(
                do_ocr=True,
                ocr_options=EasyOcrOptions(lang=['ch_sim', 'en']),
                do_table_structure=True,
                table_structure_options=TableStructureOptions(mode=TableFormerMode.ACCURATE),
                generate_picture_images=True
            )
        else:
            options = PdfPipelineOptions(
                do_ocr=False,
                do_table_structure=True,
                table_structure_options=TableStructureOptions(mode=TableFormerMode.FAST),
                generate_picture_images=True
            )
        
        parser = PDFParser(parser="docling", parser_kwargs={"pipeline_options": options})
        
        try:
            outputs = parser.run(pdf_path, modalities=["text", "tables", "images"])
            if outputs:
                pdf_name = os.path.splitext(os.path.basename(pdf_path))[0]
                saved_files = self._save_results(outputs[0], pdf_name, "docling", dirs)
                print(f"✅ Docling解析完成，保存了 {len(saved_files)} 个文件")
                return saved_files
        except Exception as e:
            print(f"❌ Docling解析失败: {e}")
        
        return []
    
    def compare_parsers(self, pdf_path):
        """对比不同解析器的结果"""
        print(f"\\n🔄 开始解析器对比测试...")
        
        comparison_dir = os.path.join(self.base_output_dir, "comparison")
        os.makedirs(comparison_dir, exist_ok=True)
        
        results = {}
        
        # 测试PyMuPDF
        import time
        start_time = time.time()
        pymupdf_files = self.parse_with_pymupdf(pdf_path)
        pymupdf_time = time.time() - start_time
        results['pymupdf'] = {'time': pymupdf_time, 'files': len(pymupdf_files)}
        
        # 测试Docling
        start_time = time.time()
        docling_files = self.parse_with_docling(pdf_path, use_ocr=False)
        docling_time = time.time() - start_time
        results['docling'] = {'time': docling_time, 'files': len(docling_files)}
        
        # 生成对比报告
        report_file = os.path.join(comparison_dir, f"comparison_report_{self.timestamp}.md")
        self._create_comparison_report(results, pdf_path, report_file)
        
        print(f"\\n📊 对比报告已生成: {report_file}")
        return results
    
    def _create_comparison_report(self, results, pdf_path, report_file):
        """创建对比报告"""
        content = [
            f"# PDF解析器对比报告",
            "",
            f"**测试文件**: {pdf_path}",
            f"**测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "## 📊 性能对比",
            "",
            "| 解析器 | 处理时间 | 生成文件数 | 速度评级 |",
            "|--------|----------|------------|----------|"
        ]
        
        for parser, data in results.items():
            speed_rating = "⚡ 很快" if data['time'] < 5 else "🐌 较慢" if data['time'] > 60 else "🚀 中等"
            content.append(f"| {parser.upper()} | {data['time']:.2f}秒 | {data['files']} | {speed_rating} |")
        
        content.extend([
            "",
            "## 💡 使用建议",
            "",
            "- **PyMuPDF**: 适合快速处理和批量操作",
            "- **Docling**: 适合高精度解析和复杂文档",
            "",
            "---",
            "",
            "*此报告由组织化PDF解析器自动生成*"
        ])
        
        with open(report_file, "w", encoding="utf-8") as f:
            f.write("\\n".join(content))

def main():
    """主函数"""
    print("🎯 组织化PDF解析工具")
    print("=" * 50)
    
    # 检查PDF文件
    pdf_file = "A.pdf"
    if not os.path.exists(pdf_file):
        print(f"❌ 错误: 找不到 {pdf_file} 文件")
        return
    
    # 创建解析器
    parser = OrganizedPDFParser()
    
    # 运行对比测试
    results = parser.compare_parsers(pdf_file)
    
    print(f"\\n🎉 解析完成!")
    print(f"📁 所有结果已保存到 outputs/ 目录")
    print(f"📊 查看对比报告了解详细信息")

if __name__ == "__main__":
    main()
'''
    
    try:
        with open("scripts/organized_parser.py", "w", encoding="utf-8") as f:
            f.write(script_content)
        print(f"  ✓ 创建解析脚本: scripts/organized_parser.py")
        return True
    except Exception as e:
        print(f"  ❌ 创建解析脚本失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 PDF解析文件清理和重组工具")
    print("=" * 60)
    
    # 1. 清理旧文件
    cleaned_files, cleaned_dirs = cleanup_old_files()
    
    # 2. 创建新目录结构
    created_dirs = create_directory_structure()
    
    # 3. 创建组织化脚本
    script_created = create_organized_parser_script()
    
    print(f"\n🎉 清理和重组完成!")
    print(f"📊 统计信息:")
    print(f"   - 清理文件: {cleaned_files} 个")
    print(f"   - 清理目录: {cleaned_dirs} 个") 
    print(f"   - 创建目录: {created_dirs} 个")
    print(f"   - 创建脚本: {'成功' if script_created else '失败'}")
    
    print(f"\n📁 新的目录结构:")
    print(f"   outputs/")
    print(f"   ├── pymupdf/     (PyMuPDF解析结果)")
    print(f"   ├── docling/     (Docling解析结果)")
    print(f"   ├── llama/       (LlamaParse解析结果)")
    print(f"   ├── anthropic/   (Anthropic解析结果)")
    print(f"   └── comparison/  (解析器对比结果)")
    print(f"   scripts/         (解析脚本)")
    
    print(f"\n🚀 接下来你可以:")
    print(f"   1. 运行 python scripts/organized_parser.py 进行组织化解析")
    print(f"   2. 查看 outputs/README.md 了解目录结构")
    print(f"   3. 在 outputs/ 目录中查看分类整理的解析结果")

if __name__ == "__main__":
    main()
