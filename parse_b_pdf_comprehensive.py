#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
B.pdf 综合解析脚本
使用PyMuPDF和Docling解析B.pdf，保存所有解析过程文件
"""

import os
import sys
import json
import time
from pathlib import Path
from datetime import datetime

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from parsestudio.parse import PDFParser
from docling.datamodel.pipeline_options import (
    PdfPipelineOptions, 
    TableStructureOptions, 
    TableFormerMode
)

class ComprehensivePDFParser:
    """综合PDF解析器，保存所有过程文件"""
    
    def __init__(self, pdf_name="B"):
        self.pdf_name = pdf_name
        self.pdf_path = f"{pdf_name}.pdf"
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建输出目录结构
        self.base_dir = f"outputs/{pdf_name.lower()}_comprehensive"
        self.create_directory_structure()
        
        # 解析日志
        self.parse_log = []
    
    def create_directory_structure(self):
        """创建完整的目录结构"""
        directories = [
            f"{self.base_dir}",
            f"{self.base_dir}/pymupdf",
            f"{self.base_dir}/pymupdf/text",
            f"{self.base_dir}/pymupdf/tables",
            f"{self.base_dir}/pymupdf/images", 
            f"{self.base_dir}/pymupdf/markdown",
            f"{self.base_dir}/pymupdf/raw_data",
            
            f"{self.base_dir}/docling",
            f"{self.base_dir}/docling/text",
            f"{self.base_dir}/docling/tables",
            f"{self.base_dir}/docling/images",
            f"{self.base_dir}/docling/markdown",
            f"{self.base_dir}/docling/raw_data",
            
            f"{self.base_dir}/comparison",
            f"{self.base_dir}/process_logs",
            f"{self.base_dir}/metadata"
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
        
        print(f"✅ 创建目录结构: {self.base_dir}")
    
    def log_process(self, step, message, data=None):
        """记录解析过程"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "step": step,
            "message": message,
            "data": data
        }
        self.parse_log.append(log_entry)
        print(f"📝 {step}: {message}")
    
    def save_raw_data(self, parser_name, result, processing_time):
        """保存原始解析数据"""
        raw_data_dir = f"{self.base_dir}/{parser_name}/raw_data"
        
        # 保存解析结果的元数据
        metadata = {
            "parser": parser_name,
            "pdf_file": self.pdf_path,
            "processing_time": processing_time,
            "timestamp": datetime.now().isoformat(),
            "text_length": len(result.text.text) if result.text and result.text.text else 0,
            "table_count": len(result.tables) if result.tables else 0,
            "image_count": len(result.images) if result.images else 0
        }
        
        with open(f"{raw_data_dir}/metadata.json", "w", encoding="utf-8") as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        # 保存原始文本
        if result.text and result.text.text:
            with open(f"{raw_data_dir}/raw_text.txt", "w", encoding="utf-8") as f:
                f.write(result.text.text)
        
        # 保存表格原始数据
        if result.tables:
            tables_data = []
            for i, table in enumerate(result.tables):
                table_info = {
                    "index": i,
                    "markdown": table.markdown,
                    "metadata": {
                        "page_number": table.metadata.page_number if table.metadata else None,
                        "bbox": table.metadata.bbox if table.metadata else None
                    }
                }
                tables_data.append(table_info)
            
            with open(f"{raw_data_dir}/tables_data.json", "w", encoding="utf-8") as f:
                json.dump(tables_data, f, indent=2, ensure_ascii=False)
        
        # 保存图像元数据
        if result.images:
            images_data = []
            for i, image in enumerate(result.images):
                image_info = {
                    "index": i,
                    "size": image.image.size,
                    "mode": image.image.mode,
                    "metadata": {
                        "page_number": image.metadata.page_number if image.metadata else None,
                        "bbox": image.metadata.bbox if image.metadata else None
                    }
                }
                images_data.append(image_info)
            
            with open(f"{raw_data_dir}/images_data.json", "w", encoding="utf-8") as f:
                json.dump(images_data, f, indent=2, ensure_ascii=False)
        
        self.log_process(f"{parser_name}_raw_data", f"保存原始数据到 {raw_data_dir}")
    
    def save_processed_files(self, parser_name, result):
        """保存处理后的文件"""
        base_path = f"{self.base_dir}/{parser_name}"
        
        # 保存文本文件
        if result.text and result.text.text:
            text_file = f"{base_path}/text/{self.pdf_name}_text.txt"
            with open(text_file, "w", encoding="utf-8") as f:
                f.write(result.text.text)
            self.log_process(f"{parser_name}_text", f"保存文本文件: {text_file}")
        
        # 保存表格文件
        if result.tables:
            for i, table in enumerate(result.tables, 1):
                # Markdown格式
                if table.markdown:
                    md_file = f"{base_path}/tables/{self.pdf_name}_table_{i}.md"
                    with open(md_file, "w", encoding="utf-8") as f:
                        f.write(f"# 表格 {i}\n\n")
                        if table.metadata and table.metadata.page_number:
                            f.write(f"*位置: 第{table.metadata.page_number}页*\n\n")
                        f.write(table.markdown)
                    self.log_process(f"{parser_name}_table", f"保存表格 {i} Markdown: {md_file}")
                
                # CSV格式
                if table.dataframe is not None and not table.dataframe.empty:
                    csv_file = f"{base_path}/tables/{self.pdf_name}_table_{i}.csv"
                    table.dataframe.to_csv(csv_file, index=False, encoding="utf-8")
                    self.log_process(f"{parser_name}_table", f"保存表格 {i} CSV: {csv_file}")
        
        # 保存图像文件
        if result.images:
            for i, image_element in enumerate(result.images, 1):
                page_num = image_element.metadata.page_number if image_element.metadata else i
                image_file = f"{base_path}/images/{self.pdf_name}_image_{i}_page_{page_num}.png"
                image_element.image.save(image_file)
                self.log_process(f"{parser_name}_image", f"保存图像 {i}: {image_file}")
    
    def create_formatted_markdown(self, parser_name, result):
        """创建格式化的Markdown文件"""
        markdown_file = f"{self.base_dir}/{parser_name}/markdown/{self.pdf_name}_{parser_name}_formatted.md"
        
        content = []
        
        # 文档头部
        content.extend([
            f"# {self.pdf_name} - {parser_name.upper()} 解析结果",
            "",
            f"**解析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"**解析器**: {parser_name.upper()}",
            "",
            "---",
            ""
        ])
        
        # 统计信息
        text_length = len(result.text.text) if result.text and result.text.text else 0
        table_count = len(result.tables) if result.tables else 0
        image_count = len(result.images) if result.images else 0
        
        content.extend([
            "## 📊 文档统计",
            "",
            f"- **文本长度**: {text_length:,} 字符",
            f"- **表格数量**: {table_count} 个",
            f"- **图像数量**: {image_count} 个",
            "",
            "---",
            ""
        ])
        
        # 文档内容
        if result.text and result.text.text:
            # 简单的文本格式化
            formatted_text = self.format_text_content(result.text.text)
            content.extend([
                "## 📄 文档内容",
                "",
                formatted_text,
                "",
                "---",
                ""
            ])
        
        # 表格部分
        if result.tables:
            content.extend([
                "## 📊 表格数据",
                ""
            ])
            
            for i, table in enumerate(result.tables, 1):
                content.extend([
                    f"### 表格 {i}",
                    ""
                ])
                
                if table.metadata and table.metadata.page_number:
                    content.extend([
                        f"*位置: 第{table.metadata.page_number}页*",
                        ""
                    ])
                
                if table.markdown:
                    content.extend([
                        table.markdown,
                        "",
                        "---",
                        ""
                    ])
        
        # 图像部分
        if result.images:
            content.extend([
                "## 🖼️ 图像内容",
                ""
            ])
            
            for i, image_element in enumerate(result.images, 1):
                page_num = image_element.metadata.page_number if image_element.metadata else i
                content.extend([
                    f"### 图像 {i} (第{page_num}页)",
                    "",
                    f"![图像{i}](../images/{self.pdf_name}_image_{i}_page_{page_num}.png)",
                    "",
                    f"- **尺寸**: {image_element.image.size[0]} × {image_element.image.size[1]}",
                    f"- **模式**: {image_element.image.mode}",
                    "",
                    "---",
                    ""
                ])
        
        # 文档尾部
        content.extend([
            "---",
            "",
            f"*本文档由 {parser_name.upper()} 解析器自动生成*",
            f"*生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*"
        ])
        
        # 写入文件
        with open(markdown_file, "w", encoding="utf-8") as f:
            f.write('\n'.join(content))
        
        self.log_process(f"{parser_name}_markdown", f"生成格式化Markdown: {markdown_file}")
        return markdown_file
    
    def format_text_content(self, text):
        """格式化文本内容"""
        if not text:
            return ""
        
        lines = text.split('\n')
        formatted_lines = []
        
        for line in lines:
            line = line.strip()
            if line:
                # 简单的标题检测
                if len(line) < 50 and any(keyword in line for keyword in 
                    ['报告', '声明', '信息', '检测', '装置', '基本', '情况']):
                    formatted_lines.append(f"## {line}")
                else:
                    formatted_lines.append(line)
            else:
                formatted_lines.append("")
        
        return '\n'.join(formatted_lines)
    
    def parse_with_pymupdf(self):
        """使用PyMuPDF解析"""
        self.log_process("pymupdf_start", f"开始使用PyMuPDF解析 {self.pdf_path}")
        
        start_time = time.time()
        
        try:
            parser = PDFParser(parser="pymupdf")
            outputs = parser.run(self.pdf_path, modalities=["text", "tables", "images"])
            
            processing_time = time.time() - start_time
            
            if outputs:
                result = outputs[0]
                
                # 保存原始数据
                self.save_raw_data("pymupdf", result, processing_time)
                
                # 保存处理后的文件
                self.save_processed_files("pymupdf", result)
                
                # 创建格式化Markdown
                markdown_file = self.create_formatted_markdown("pymupdf", result)
                
                self.log_process("pymupdf_success", 
                    f"PyMuPDF解析完成 - 耗时: {processing_time:.2f}秒", {
                        "processing_time": processing_time,
                        "text_length": len(result.text.text) if result.text else 0,
                        "table_count": len(result.tables) if result.tables else 0,
                        "image_count": len(result.images) if result.images else 0
                    })
                
                return result, processing_time, markdown_file
            else:
                self.log_process("pymupdf_error", "PyMuPDF解析失败: 没有返回结果")
                return None, processing_time, None
                
        except Exception as e:
            processing_time = time.time() - start_time
            self.log_process("pymupdf_error", f"PyMuPDF解析异常: {str(e)}")
            return None, processing_time, None
    
    def parse_with_docling(self):
        """使用Docling解析"""
        self.log_process("docling_start", f"开始使用Docling解析 {self.pdf_path}")
        
        start_time = time.time()
        
        try:
            # 配置Docling选项
            options = PdfPipelineOptions(
                do_ocr=False,  # 禁用OCR以提高速度
                do_table_structure=True,
                table_structure_options=TableStructureOptions(mode=TableFormerMode.FAST),
                generate_picture_images=True,
                images_scale=1.0
            )
            
            parser = PDFParser(parser="docling", parser_kwargs={"pipeline_options": options})
            outputs = parser.run(self.pdf_path, modalities=["text", "tables", "images"])
            
            processing_time = time.time() - start_time
            
            if outputs:
                result = outputs[0]
                
                # 保存原始数据
                self.save_raw_data("docling", result, processing_time)
                
                # 保存处理后的文件
                self.save_processed_files("docling", result)
                
                # 创建格式化Markdown
                markdown_file = self.create_formatted_markdown("docling", result)
                
                self.log_process("docling_success", 
                    f"Docling解析完成 - 耗时: {processing_time:.2f}秒", {
                        "processing_time": processing_time,
                        "text_length": len(result.text.text) if result.text else 0,
                        "table_count": len(result.tables) if result.tables else 0,
                        "image_count": len(result.images) if result.images else 0
                    })
                
                return result, processing_time, markdown_file
            else:
                self.log_process("docling_error", "Docling解析失败: 没有返回结果")
                return None, processing_time, None
                
        except Exception as e:
            processing_time = time.time() - start_time
            self.log_process("docling_error", f"Docling解析异常: {str(e)}")
            return None, processing_time, None
