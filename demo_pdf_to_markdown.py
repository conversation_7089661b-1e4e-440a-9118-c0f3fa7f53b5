#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF转Markdown演示脚本
展示如何使用不同的解析器和选项
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from pdf_to_markdown import create_advanced_pdf_to_markdown

def demo_different_parsers():
    """演示使用不同解析器的效果"""
    
    pdf_file = "A.pdf"
    
    if not os.path.exists(pdf_file):
        print(f"❌ 错误: 文件 {pdf_file} 不存在")
        return
    
    # 获取高级转换器
    converter = create_advanced_pdf_to_markdown()
    
    # 测试不同的解析器
    parsers = ["pymupdf"]  # 只使用轻量级解析器进行演示
    
    print("🚀 开始PDF转Markdown演示")
    print("=" * 60)
    
    results = []
    
    for parser_type in parsers:
        print(f"\n📋 测试解析器: {parser_type.upper()}")
        print("-" * 40)
        
        try:
            output_file = f"A_{parser_type}_demo.md"
            result = converter(
                pdf_path=pdf_file,
                output_path=output_file,
                parser_type=parser_type,
                include_images=True
            )
            
            if result:
                file_size = os.path.getsize(result)
                results.append({
                    'parser': parser_type,
                    'file': result,
                    'size': file_size
                })
                print(f"✅ 成功生成: {result} ({file_size:,} 字节)")
            else:
                print(f"❌ 生成失败")
                
        except Exception as e:
            print(f"❌ 错误: {str(e)}")
    
    # 显示结果对比
    if results:
        print(f"\n📊 结果对比")
        print("=" * 60)
        print(f"{'解析器':<12} {'文件名':<25} {'大小':<15}")
        print("-" * 60)
        
        for result in results:
            print(f"{result['parser']:<12} {result['file']:<25} {result['size']:,} 字节")
        
        print(f"\n💡 建议:")
        print("- PyMuPDF: 速度快，适合简单文档")
        print("- Docling: 功能全面，适合复杂文档")
        print("- LlamaParse: AI增强，适合复杂布局")
        print("- Anthropic: AI驱动，适合智能解析")

def demo_custom_options():
    """演示自定义选项"""
    
    pdf_file = "A.pdf"
    converter = create_advanced_pdf_to_markdown()
    
    print(f"\n🎛️ 自定义选项演示")
    print("=" * 60)
    
    # 演示1: 只提取文本，不包含图像
    print("\n📝 演示1: 纯文本模式")
    result1 = converter(
        pdf_path=pdf_file,
        output_path="A_text_only.md",
        parser_type="pymupdf",
        include_images=False
    )
    
    if result1:
        print(f"✅ 纯文本版本已生成: {result1}")
    
    # 演示2: 包含所有内容
    print("\n📚 演示2: 完整模式")
    result2 = converter(
        pdf_path=pdf_file,
        output_path="A_complete.md",
        parser_type="pymupdf",
        include_images=True
    )
    
    if result2:
        print(f"✅ 完整版本已生成: {result2}")

def show_usage_examples():
    """显示使用示例"""
    
    print(f"\n📖 使用示例")
    print("=" * 60)
    
    examples = [
        {
            "title": "基本用法",
            "command": "python pdf_to_markdown.py A.pdf",
            "description": "使用默认设置转换PDF"
        },
        {
            "title": "指定输出文件",
            "command": "python pdf_to_markdown.py A.pdf -o my_document.md",
            "description": "指定输出文件名"
        },
        {
            "title": "使用Docling解析器",
            "command": "python pdf_to_markdown.py A.pdf -p docling",
            "description": "使用高级Docling解析器"
        },
        {
            "title": "不包含图像",
            "command": "python pdf_to_markdown.py A.pdf --no-images",
            "description": "只转换文本和表格，不包含图像"
        },
        {
            "title": "完整示例",
            "command": "python pdf_to_markdown.py document.pdf -o output.md -p docling",
            "description": "使用Docling解析器，指定输出文件"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['title']}")
        print(f"   命令: {example['command']}")
        print(f"   说明: {example['description']}")

def create_sample_usage_script():
    """创建示例使用脚本"""
    
    script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF转Markdown使用示例
"""

from pdf_to_markdown import create_advanced_pdf_to_markdown

def convert_my_pdf():
    """转换我的PDF文件"""
    
    # 创建转换器
    converter = create_advanced_pdf_to_markdown()
    
    # 转换PDF
    result = converter(
        pdf_path="your_document.pdf",  # 替换为你的PDF文件路径
        output_path="output.md",       # 输出文件名
        parser_type="pymupdf",         # 解析器类型
        include_images=True            # 是否包含图像
    )
    
    if result:
        print(f"转换成功: {result}")
    else:
        print("转换失败")

if __name__ == "__main__":
    convert_my_pdf()
'''
    
    with open("sample_usage.py", "w", encoding="utf-8") as f:
        f.write(script_content)
    
    print(f"\n📄 示例脚本已创建: sample_usage.py")
    print("你可以修改其中的文件路径来转换你自己的PDF文件")

def main():
    """主函数"""
    
    print("🎯 PDF转Markdown工具演示")
    print("=" * 60)
    
    # 演示不同解析器
    demo_different_parsers()
    
    # 演示自定义选项
    demo_custom_options()
    
    # 显示使用示例
    show_usage_examples()
    
    # 创建示例脚本
    create_sample_usage_script()
    
    print(f"\n🎉 演示完成！")
    print(f"📁 生成的文件:")
    
    # 列出生成的文件
    generated_files = [
        "A_pymupdf_demo.md",
        "A_text_only.md", 
        "A_complete.md",
        "sample_usage.py"
    ]
    
    for file in generated_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"   - {file} ({size:,} 字节)")
    
    print(f"\n💡 接下来你可以:")
    print("1. 查看生成的Markdown文件")
    print("2. 使用 python pdf_to_markdown.py --help 查看完整帮助")
    print("3. 修改 sample_usage.py 来转换你自己的PDF文件")

if __name__ == "__main__":
    main()
