#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解析 A.pdf 文件的脚本
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from parsestudio.parse import PDFParser

def parse_pdf_file(pdf_path, output_dir="output"):
    """
    解析 PDF 文件并保存结果
    
    Args:
        pdf_path (str): PDF 文件路径
        output_dir (str): 输出目录
    """
    print(f"开始解析 PDF 文件: {pdf_path}")
    
    # 检查文件是否存在
    if not os.path.exists(pdf_path):
        print(f"错误: 文件 {pdf_path} 不存在")
        return
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 初始化解析器 (使用 docling 作为默认解析器)
        print("初始化 Docling 解析器...")
        parser = PDFParser(parser="docling")
        
        # 解析 PDF 文件，提取所有类型的内容
        print("正在解析 PDF 文件...")
        outputs = parser.run(pdf_path, modalities=["text", "tables", "images"])
        
        if not outputs:
            print("警告: 没有解析到任何内容")
            return
        
        # 获取解析结果
        result = outputs[0]
        pdf_name = os.path.splitext(os.path.basename(pdf_path))[0]
        
        print(f"解析完成! 开始保存结果到 {output_dir} 目录...")
        
        # 保存文本内容
        if result.text and result.text.text:
            text_file = os.path.join(output_dir, f"{pdf_name}_text.txt")
            with open(text_file, "w", encoding="utf-8") as f:
                f.write(result.text.text)
            print(f"✓ 文本内容已保存到: {text_file}")
            print(f"  文本长度: {len(result.text.text)} 字符")
        else:
            print("⚠ 未提取到文本内容")
        
        # 保存表格
        if result.tables:
            print(f"✓ 发现 {len(result.tables)} 个表格")
            for i, table in enumerate(result.tables):
                # 保存 Markdown 格式
                if table.markdown:
                    md_file = os.path.join(output_dir, f"{pdf_name}_table_{i+1}.md")
                    with open(md_file, "w", encoding="utf-8") as f:
                        f.write(table.markdown)
                    print(f"  表格 {i+1} Markdown 已保存到: {md_file}")
                
                # 保存 CSV 格式
                if table.dataframe is not None and not table.dataframe.empty:
                    csv_file = os.path.join(output_dir, f"{pdf_name}_table_{i+1}.csv")
                    table.dataframe.to_csv(csv_file, index=False, encoding="utf-8")
                    print(f"  表格 {i+1} CSV 已保存到: {csv_file}")
                    print(f"    表格尺寸: {table.dataframe.shape}")
                
                # 打印表格元数据
                if table.metadata:
                    print(f"    页码: {table.metadata.page_number}")
                    print(f"    位置: {table.metadata.bbox}")
        else:
            print("⚠ 未发现表格")
        
        # 保存图像
        if result.images:
            print(f"✓ 发现 {len(result.images)} 个图像")
            for i, image_element in enumerate(result.images):
                image_file = os.path.join(output_dir, f"{pdf_name}_image_{i+1}_page_{image_element.metadata.page_number}.png")
                image_element.image.save(image_file)
                print(f"  图像 {i+1} 已保存到: {image_file}")
                print(f"    尺寸: {image_element.image.size}")
                print(f"    页码: {image_element.metadata.page_number}")
                print(f"    位置: {image_element.metadata.bbox}")
        else:
            print("⚠ 未发现图像")
        
        # 生成解析报告
        report_file = os.path.join(output_dir, f"{pdf_name}_report.txt")
        with open(report_file, "w", encoding="utf-8") as f:
            f.write(f"PDF 解析报告\n")
            f.write(f"=" * 50 + "\n")
            f.write(f"文件名: {pdf_path}\n")
            f.write(f"解析器: Docling\n")
            f.write(f"解析时间: {__import__('datetime').datetime.now()}\n\n")
            
            f.write(f"解析结果统计:\n")
            f.write(f"- 文本长度: {len(result.text.text) if result.text and result.text.text else 0} 字符\n")
            f.write(f"- 表格数量: {len(result.tables) if result.tables else 0}\n")
            f.write(f"- 图像数量: {len(result.images) if result.images else 0}\n\n")
            
            if result.tables:
                f.write(f"表格详情:\n")
                for i, table in enumerate(result.tables):
                    f.write(f"  表格 {i+1}:\n")
                    f.write(f"    页码: {table.metadata.page_number if table.metadata else 'N/A'}\n")
                    f.write(f"    位置: {table.metadata.bbox if table.metadata else 'N/A'}\n")
                    f.write(f"    行列数: {table.dataframe.shape if table.dataframe is not None else 'N/A'}\n\n")
            
            if result.images:
                f.write(f"图像详情:\n")
                for i, image in enumerate(result.images):
                    f.write(f"  图像 {i+1}:\n")
                    f.write(f"    页码: {image.metadata.page_number}\n")
                    f.write(f"    位置: {image.metadata.bbox}\n")
                    f.write(f"    尺寸: {image.image.size}\n\n")
        
        print(f"✓ 解析报告已保存到: {report_file}")
        print(f"\n🎉 PDF 解析完成! 所有结果已保存到 '{output_dir}' 目录")
        
    except Exception as e:
        print(f"❌ 解析过程中出现错误: {str(e)}")
        print(f"错误类型: {type(e).__name__}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    pdf_file = "A.pdf"
    output_directory = "parsed_output"
    
    print("=" * 60)
    print("PDF 解析工具")
    print("=" * 60)
    
    parse_pdf_file(pdf_file, output_directory)

if __name__ == "__main__":
    main()
