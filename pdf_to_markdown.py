#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将PDF解析后的内容按照原始格式生成Markdown文件
"""

import os
import sys
import re
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from parsestudio.parse import PDFParser

def clean_text(text):
    """清理文本，移除多余的空行和空格"""
    if not text:
        return ""

    # 分割成行
    lines = text.split('\n')
    cleaned_lines = []

    for line in lines:
        # 移除行首尾空格
        cleaned_line = line.strip()
        cleaned_lines.append(cleaned_line)

    # 移除连续的空行，只保留一个
    result_lines = []
    prev_empty = False

    for line in cleaned_lines:
        if line == "":
            if not prev_empty:
                result_lines.append(line)
            prev_empty = True
        else:
            result_lines.append(line)
            prev_empty = False

    return '\n'.join(result_lines)

def format_table_for_markdown(table_element):
    """将表格格式化为更好的Markdown格式"""
    if not table_element.markdown:
        return ""

    # 获取原始markdown
    markdown = table_element.markdown

    # 添加表格标题和元数据
    result = []
    if table_element.metadata and table_element.metadata.page_number:
        result.append(f"*表格位置: 第{table_element.metadata.page_number}页*")
        result.append("")

    result.append(markdown)
    result.append("")

    return '\n'.join(result)

def extract_page_content(text, page_markers=None):
    """尝试按页分割内容"""
    if not page_markers:
        # 常见的页码标记模式
        page_patterns = [
            r'第\s*(\d+)\s*页',  # 第X页
            r'Page\s*(\d+)',     # Page X
            r'- \d+ -',          # - X -
            r'\d+\s*/\s*\d+',    # X/Y
        ]

        pages = {}
        current_page = 1
        lines = text.split('\n')
        current_content = []

        for line in lines:
            # 检查是否是页码标记
            is_page_marker = False
            for pattern in page_patterns:
                match = re.search(pattern, line)
                if match:
                    # 保存当前页内容
                    if current_content:
                        pages[current_page] = '\n'.join(current_content)

                    # 开始新页
                    if pattern == r'第\s*(\d+)\s*页':
                        current_page = int(match.group(1))
                    else:
                        current_page += 1
                    current_content = []
                    is_page_marker = True
                    break

            if not is_page_marker:
                current_content.append(line)

        # 保存最后一页
        if current_content:
            pages[current_page] = '\n'.join(current_content)

        return pages

    return {1: text}

def pdf_to_markdown(pdf_path, output_path=None, include_images=True):
    """
    将PDF转换为结构化的Markdown文件

    Args:
        pdf_path (str): PDF文件路径
        output_path (str): 输出Markdown文件路径
        include_images (bool): 是否包含图像
    """
    print(f"开始解析PDF文件: {pdf_path}")

    if not os.path.exists(pdf_path):
        print(f"错误: 文件 {pdf_path} 不存在")
        return

    # 设置输出路径
    if not output_path:
        pdf_name = os.path.splitext(os.path.basename(pdf_path))[0]
        output_path = f"{pdf_name}_formatted.md"

    try:
        # 初始化解析器
        print("初始化解析器...")
        parser = PDFParser(parser="pymupdf")  # 使用轻量级解析器

        # 解析PDF
        print("正在解析PDF...")
        modalities = ["text", "tables"]
        if include_images:
            modalities.append("images")

        outputs = parser.run(pdf_path, modalities=modalities)

        if not outputs:
            print("错误: 没有解析到任何内容")
            return

        result = outputs[0]
        pdf_name = os.path.splitext(os.path.basename(pdf_path))[0]

        print("正在生成Markdown文件...")

        # 开始构建Markdown内容
        markdown_content = []

        # 添加文档标题和元信息
        markdown_content.append(f"# {pdf_name}")
        markdown_content.append("")
        markdown_content.append(f"*原始文件: {pdf_path}*")
        markdown_content.append(f"*生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*")
        markdown_content.append("")
        markdown_content.append("---")
        markdown_content.append("")

        # 添加文档统计信息
        markdown_content.append("## 📊 文档统计")
        markdown_content.append("")
        markdown_content.append(f"- **文本长度**: {len(result.text.text) if result.text and result.text.text else 0:,} 字符")
        markdown_content.append(f"- **表格数量**: {len(result.tables) if result.tables else 0}")
        markdown_content.append(f"- **图像数量**: {len(result.images) if result.images else 0}")
        markdown_content.append("")
        markdown_content.append("---")
        markdown_content.append("")

        # 处理文本内容
        if result.text and result.text.text:
            print("处理文本内容...")

            # 清理文本
            cleaned_text = clean_text(result.text.text)

            # 尝试按页分割内容
            pages = extract_page_content(cleaned_text)

            if len(pages) > 1:
                markdown_content.append("## 📄 文档内容")
                markdown_content.append("")

                for page_num in sorted(pages.keys()):
                    page_content = pages[page_num].strip()
                    if page_content:
                        markdown_content.append(f"### 第 {page_num} 页")
                        markdown_content.append("")

                        # 将页面内容按段落分割
                        paragraphs = page_content.split('\n\n')
                        for paragraph in paragraphs:
                            paragraph = paragraph.strip()
                            if paragraph:
                                # 检查是否是标题（简单启发式）
                                if (len(paragraph) < 50 and
                                    not paragraph.endswith('。') and
                                    not paragraph.endswith('.') and
                                    '：' not in paragraph and
                                    ':' not in paragraph):
                                    markdown_content.append(f"**{paragraph}**")
                                else:
                                    markdown_content.append(paragraph)
                                markdown_content.append("")

                        markdown_content.append("---")
                        markdown_content.append("")
            else:
                markdown_content.append("## 📄 文档内容")
                markdown_content.append("")

                # 处理整个文档作为一个单元
                paragraphs = cleaned_text.split('\n\n')
                for paragraph in paragraphs:
                    paragraph = paragraph.strip()
                    if paragraph:
                        # 检查是否是标题
                        if (len(paragraph) < 50 and
                            not paragraph.endswith('。') and
                            not paragraph.endswith('.') and
                            '：' not in paragraph and
                            ':' not in paragraph):
                            markdown_content.append(f"**{paragraph}**")
                        else:
                            markdown_content.append(paragraph)
                        markdown_content.append("")

        # 处理表格
        if result.tables:
            print(f"处理 {len(result.tables)} 个表格...")
            markdown_content.append("## 📊 表格数据")
            markdown_content.append("")

            for i, table in enumerate(result.tables, 1):
                markdown_content.append(f"### 表格 {i}")
                markdown_content.append("")

                formatted_table = format_table_for_markdown(table)
                if formatted_table:
                    markdown_content.append(formatted_table)
                else:
                    markdown_content.append("*表格内容无法显示*")
                    markdown_content.append("")

                markdown_content.append("---")
                markdown_content.append("")

        # 处理图像
        if include_images and result.images:
            print(f"处理 {len(result.images)} 个图像...")
            markdown_content.append("## 🖼️ 图像内容")
            markdown_content.append("")

            # 创建图像目录
            image_dir = f"{os.path.splitext(output_path)[0]}_images"
            os.makedirs(image_dir, exist_ok=True)

            for i, image_element in enumerate(result.images, 1):
                # 保存图像
                image_filename = f"image_{i}_page_{image_element.metadata.page_number}.png"
                image_path = os.path.join(image_dir, image_filename)
                image_element.image.save(image_path)

                # 添加到Markdown
                markdown_content.append(f"### 图像 {i}")
                markdown_content.append("")
                markdown_content.append(f"![图像 {i}]({image_dir}/{image_filename})")
                markdown_content.append("")
                markdown_content.append(f"- **页码**: {image_element.metadata.page_number}")
                markdown_content.append(f"- **尺寸**: {image_element.image.size[0]} × {image_element.image.size[1]}")
                if image_element.metadata.bbox:
                    markdown_content.append(f"- **位置**: {image_element.metadata.bbox}")
                markdown_content.append("")
                markdown_content.append("---")
                markdown_content.append("")

        # 添加页脚
        markdown_content.append("---")
        markdown_content.append("")
        markdown_content.append("*此文档由 ParseStudio 自动生成*")

        # 写入文件
        final_content = '\n'.join(markdown_content)
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(final_content)

        print(f"✅ Markdown文件已生成: {output_path}")

        # 显示统计信息
        print(f"\n📊 生成统计:")
        print(f"   输出文件大小: {len(final_content):,} 字符")
        print(f"   Markdown行数: {len(markdown_content):,}")
        if include_images and result.images:
            print(f"   图像目录: {image_dir}")

        return output_path

    except Exception as e:
        print(f"❌ 转换过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def create_advanced_pdf_to_markdown():
    """创建高级版本的PDF转Markdown工具"""

    def advanced_pdf_to_markdown(pdf_path, output_path=None, parser_type="pymupdf", include_images=True):
        """
        高级PDF转Markdown工具，支持多种解析器和自定义选项

        Args:
            pdf_path (str): PDF文件路径
            output_path (str): 输出文件路径
            parser_type (str): 解析器类型 ("pymupdf", "docling", "llama", "anthropic")
            include_images (bool): 是否包含图像
        """
        print(f"🚀 高级PDF转Markdown工具")
        print(f"📄 输入文件: {pdf_path}")
        print(f"🔧 解析器: {parser_type}")
        print(f"🖼️ 包含图像: {'是' if include_images else '否'}")
        print("-" * 50)

        if not os.path.exists(pdf_path):
            print(f"❌ 错误: 文件 {pdf_path} 不存在")
            return None

        # 设置输出路径
        if not output_path:
            pdf_name = os.path.splitext(os.path.basename(pdf_path))[0]
            output_path = f"{pdf_name}_{parser_type}_formatted.md"

        try:
            # 初始化解析器
            print(f"🔄 初始化 {parser_type} 解析器...")
            parser = PDFParser(parser=parser_type)

            # 解析PDF
            print("📖 正在解析PDF文件...")
            modalities = ["text", "tables"]
            if include_images:
                modalities.append("images")

            outputs = parser.run(pdf_path, modalities=modalities)

            if not outputs:
                print("⚠️ 警告: 没有解析到任何内容")
                return None

            result = outputs[0]
            pdf_name = os.path.splitext(os.path.basename(pdf_path))[0]

            print("✍️ 正在生成Markdown文件...")

            # 构建Markdown内容
            markdown_content = []

            # 文档头部
            markdown_content.extend([
                f"# {pdf_name}",
                "",
                f"*原始文件: {pdf_path}*",
                f"*解析器: {parser_type.upper()}*",
                f"*生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*",
                "",
                "---",
                ""
            ])

            # 文档统计
            text_length = len(result.text.text) if result.text and result.text.text else 0
            table_count = len(result.tables) if result.tables else 0
            image_count = len(result.images) if result.images else 0

            markdown_content.extend([
                "## 📊 文档统计",
                "",
                f"- **文本长度**: {text_length:,} 字符",
                f"- **表格数量**: {table_count}",
                f"- **图像数量**: {image_count}",
                f"- **解析器**: {parser_type.upper()}",
                "",
                "---",
                ""
            ])

            # 目录
            if text_length > 0 or table_count > 0 or image_count > 0:
                markdown_content.extend([
                    "## 📑 目录",
                    ""
                ])

                if text_length > 0:
                    markdown_content.append("- [📄 文档内容](#-文档内容)")
                if table_count > 0:
                    markdown_content.append("- [📊 表格数据](#-表格数据)")
                if image_count > 0:
                    markdown_content.append("- [🖼️ 图像内容](#️-图像内容)")

                markdown_content.extend(["", "---", ""])

            # 处理文本内容
            if result.text and result.text.text:
                print(f"📝 处理文本内容 ({text_length:,} 字符)...")

                cleaned_text = clean_text(result.text.text)
                pages = extract_page_content(cleaned_text)

                markdown_content.extend([
                    "## 📄 文档内容",
                    ""
                ])

                if len(pages) > 1:
                    for page_num in sorted(pages.keys()):
                        page_content = pages[page_num].strip()
                        if page_content:
                            markdown_content.extend([
                                f"### 第 {page_num} 页",
                                "",
                                page_content,
                                "",
                                "---",
                                ""
                            ])
                else:
                    markdown_content.extend([
                        cleaned_text,
                        "",
                        "---",
                        ""
                    ])

            # 处理表格
            if result.tables:
                print(f"📊 处理表格数据 ({table_count} 个表格)...")

                markdown_content.extend([
                    "## 📊 表格数据",
                    ""
                ])

                for i, table in enumerate(result.tables, 1):
                    markdown_content.extend([
                        f"### 表格 {i}",
                        ""
                    ])

                    if table.metadata and table.metadata.page_number:
                        markdown_content.extend([
                            f"*位置: 第{table.metadata.page_number}页*",
                            ""
                        ])

                    if table.markdown:
                        markdown_content.extend([
                            table.markdown,
                            ""
                        ])
                    else:
                        markdown_content.extend([
                            "*表格内容无法显示*",
                            ""
                        ])

                    markdown_content.extend(["---", ""])

            # 处理图像
            if include_images and result.images:
                print(f"🖼️ 处理图像内容 ({image_count} 个图像)...")

                # 创建图像目录
                image_dir = f"{os.path.splitext(output_path)[0]}_images"
                os.makedirs(image_dir, exist_ok=True)

                markdown_content.extend([
                    "## 🖼️ 图像内容",
                    ""
                ])

                for i, image_element in enumerate(result.images, 1):
                    # 保存图像
                    image_filename = f"image_{i}_page_{image_element.metadata.page_number}.png"
                    image_path = os.path.join(image_dir, image_filename)
                    image_element.image.save(image_path)

                    # 添加到Markdown
                    markdown_content.extend([
                        f"### 图像 {i}",
                        "",
                        f"![图像 {i}]({image_dir}/{image_filename})",
                        "",
                        f"- **页码**: {image_element.metadata.page_number}",
                        f"- **尺寸**: {image_element.image.size[0]} × {image_element.image.size[1]}",
                        "",
                        "---",
                        ""
                    ])

            # 页脚
            markdown_content.extend([
                "---",
                "",
                f"*此文档由 ParseStudio ({parser_type.upper()}) 自动生成*"
            ])

            # 写入文件
            final_content = '\n'.join(markdown_content)
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(final_content)

            print(f"✅ Markdown文件已生成: {output_path}")
            print(f"📊 文件大小: {len(final_content):,} 字符")
            print(f"📄 总行数: {len(markdown_content):,}")

            return output_path

        except Exception as e:
            print(f"❌ 转换过程中出现错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return None

    return advanced_pdf_to_markdown

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='PDF转Markdown工具')
    parser.add_argument('pdf_file', nargs='?', default='A.pdf', help='PDF文件路径')
    parser.add_argument('-o', '--output', help='输出文件路径')
    parser.add_argument('-p', '--parser', choices=['pymupdf', 'docling', 'llama', 'anthropic'],
                       default='pymupdf', help='解析器类型')
    parser.add_argument('--no-images', action='store_true', help='不包含图像')

    args = parser.parse_args()

    print("=" * 60)
    print("PDF 转 Markdown 工具")
    print("=" * 60)

    # 使用高级版本
    advanced_converter = create_advanced_pdf_to_markdown()
    result = advanced_converter(
        args.pdf_file,
        args.output,
        args.parser,
        not args.no_images
    )

    if result:
        print(f"\n🎉 转换完成！")
        print(f"📁 输出文件: {result}")
        print(f"💡 你可以使用任何Markdown编辑器查看生成的文件")
        print(f"🌐 推荐使用 Typora、Mark Text 或 VS Code 预览")

if __name__ == "__main__":
    main()
