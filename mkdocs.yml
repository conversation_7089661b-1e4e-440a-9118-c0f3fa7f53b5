site_name: ParseStudio

theme:
  name: material
  font:
    text: Roboto
    code: Roboto Mono
  icon:
    logo: material/tree
  features:
    - navigation.tabs
    - navigation.sections
    - toc.integrate
    - navigation.top
    - search.suggest
    - search.highlight
    - search
    - content.tabs.link
    - content.code.annotation
    - content.code.copy
    - content.code.tabbed
    - content.image.figure
    - content.image.lightbox
    - content.image.zoom
    - content.video
    - content.iframe
    - content.math
  language: en
  palette:
    - scheme: default
      toggle:
        icon: material/toggle-switch-off-outline 
        name: Switch to dark mode
      primary: lime
      accent: purple 
    - scheme: slate 
      toggle:
        icon: material/toggle-switch
        name: Switch to light mode    
      primary: purple-grey
      accent: lime

plugins:
  - search:
      lang: en
  - mkdocstrings:
      handlers:
        python:
          options:
            filters: [""]
            show_root_heading: false
            show_root_toc_entry: false
  - autorefs


nav:
  - Home:
    - Welcome to Parse Studio: 
      - Introduction: index.md
      - Installation: installation.md
  - Docling Parser:
      - Overview: parsers/docling_parser.md
  - Llama Parser:
      - Overview: parsers/llama_parser.md
  - PymuPDF Parser:
      - Overview: parsers/pymupdf_parser.md
  - Schemas: schemas.md



markdown_extensions:
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - pymdownx.superfences
  - admonition
